#!/usr/bin/env python3

import json
import requests
import time

def demo_friendly_logging():
    """演示友好的请求响应日志显示"""
    print("🎭 Friendly Logging Demo")
    print("━" * 60)
    print("This demo shows how the server displays requests and responses")
    print("in a user-friendly format with emojis and structured output.")
    print("━" * 60)
    
    base_url = "http://localhost:8080"
    
    # 演示 1: OpenAI API 请求
    print("\n🤖 Demo 1: OpenAI Chat Completion")
    print("Expected log format:")
    print("🤖 OpenAI Chat Completion Request")
    print("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
    print("📋 Model: gpt-4")
    print("🌡️  Temperature: 0.7")
    print("🎯 Max Tokens: 100")
    print("🔄 Stream: false")
    print("")
    print("💬 Messages:")
    print("  1. 👤 User: Hello, how are you?")
    print("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
    
    openai_request = {
        "model": "gpt-4",
        "messages": [
            {
                "role": "user",
                "content": "Hello, how are you?"
            }
        ],
        "temperature": 0.7,
        "max_tokens": 100,
        "stream": False
    }
    
    print("\n🚀 Sending OpenAI request...")
    send_request(base_url + "/v1/chat/completions", openai_request)
    
    time.sleep(2)
    
    # 演示 2: Claude API 请求
    print("\n🧠 Demo 2: Claude Message Request")
    print("Expected log format:")
    print("🧠 Claude Message Request")
    print("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
    print("📋 Model: claude-3.5-sonnet")
    print("🌡️  Temperature: 0.8")
    print("🎯 Max Tokens: 150")
    print("🔄 Stream: false")
    print("🔧 System: You are a helpful assistant.")
    print("")
    print("💬 Messages:")
    print("  1. 👤 User: What is the capital of France?")
    print("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
    
    claude_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 150,
        "system": "You are a helpful assistant.",
        "messages": [
            {
                "role": "user",
                "content": "What is the capital of France?"
            }
        ],
        "temperature": 0.8,
        "stream": False
    }
    
    print("\n🚀 Sending Claude request...")
    send_request(base_url + "/v1/messages", claude_request)
    
    time.sleep(2)
    
    # 演示 3: 复杂内容块请求
    print("\n📝 Demo 3: Complex Content Blocks")
    print("Expected log format:")
    print("🧠 Claude Message Request")
    print("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
    print("📋 Model: claude-3.5-sonnet")
    print("🎯 Max Tokens: 100")
    print("")
    print("💬 Messages:")
    print("  1. 👤 User: Please analyze this code snippet.")
    print("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
    
    complex_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 100,
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "Please analyze this code snippet."
                    }
                ]
            }
        ],
        "stream": False
    }
    
    print("\n🚀 Sending complex content request...")
    send_request(base_url + "/v1/messages", complex_request)
    
    print("\n━" * 60)
    print("🎉 Demo completed!")
    print("Check the server console to see the actual friendly log output.")
    print("━" * 60)

def send_request(url, data):
    """发送请求并显示结果"""
    try:
        response = requests.post(url, json=data, timeout=10)
        
        if response.status_code == 200:
            print(f"✅ Request successful (200)")
            try:
                response_data = response.json()
                if 'choices' in response_data:  # OpenAI format
                    print(f"🤖 OpenAI Response received")
                elif 'content' in response_data:  # Claude format
                    print(f"🧠 Claude Response received")
            except:
                print("📄 Response received (non-JSON)")
        else:
            print(f"⚠️  Status: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - make sure server is running on http://localhost:8080")
    except requests.exceptions.Timeout:
        print("⏰ Request timeout")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🎭 Friendly Logging Demo")
    print("This script demonstrates the server's friendly request/response logging.")
    print("Start the server first: java -jar build/libs/llm-provider.jar")
    print("Then run this script to see the friendly logs in action.")
    print("")
    
    input("Press Enter to start the demo (make sure server is running)...")
    
    demo_friendly_logging()
