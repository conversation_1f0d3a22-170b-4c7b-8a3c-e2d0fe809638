#!/usr/bin/env python3

import json
import requests
import time

def test_debug_mode_and_cache_control():
    """测试 debug 模式和 cache_control 字段处理"""
    print("🔍 Testing Debug Mode and Cache Control")
    print("━" * 60)
    
    base_url = "http://localhost:8080"
    
    # 测试 1: 带 cache_control 的请求
    print("📝 Test 1: Request with cache_control")
    cache_control_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 100,
        "system": [
            {
                "type": "text",
                "text": "You are a helpful assistant.",
                "cache_control": {
                    "type": "ephemeral"
                }
            }
        ],
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "Hello, how are you?",
                        "cache_control": {
                            "type": "ephemeral"
                        }
                    }
                ],
                "cache_control": {
                    "type": "ephemeral"
                }
            }
        ],
        "cache_control": {
            "type": "ephemeral"
        },
        "stream": False
    }
    
    print("🚀 Sending request with cache_control...")
    print("Expected: Server should print detailed debug information")
    test_request(base_url, cache_control_request, "Cache Control Request")
    
    # 测试 2: 带 metadata 的请求
    print("\n📝 Test 2: Request with metadata")
    metadata_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 100,
        "system": "You are helpful.",
        "messages": [
            {
                "role": "user",
                "content": "What is the weather like?"
            }
        ],
        "metadata": {
            "user_id": "test_user_123",
            "session_id": "session_456",
            "request_id": "req_789"
        },
        "stream": False
    }
    
    print("🚀 Sending request with metadata...")
    test_request(base_url, metadata_request, "Metadata Request")
    
    # 测试 3: 复杂的组合请求
    print("\n📝 Test 3: Complex combined request")
    complex_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 150,
        "system": [
            {
                "type": "text",
                "text": "You are an expert code reviewer.",
                "cache_control": {
                    "type": "ephemeral"
                }
            }
        ],
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "Please review this code snippet and provide feedback."
                    }
                ],
                "cache_control": {
                    "type": "ephemeral"
                }
            }
        ],
        "temperature": 0.7,
        "top_p": 0.9,
        "top_k": 40,
        "stop_sequences": ["END", "STOP"],
        "metadata": {
            "user_id": "developer_123",
            "task": "code_review"
        },
        "cache_control": {
            "type": "ephemeral"
        },
        "stream": False
    }
    
    print("🚀 Sending complex combined request...")
    test_request(base_url, complex_request, "Complex Combined Request")
    
    # 测试 4: 无效的 JSON 格式
    print("\n📝 Test 4: Invalid JSON format")
    print("🚀 Sending invalid JSON...")
    test_invalid_json(base_url)
    
    print("\n━" * 60)
    print("✨ Debug mode and cache_control tests completed!")
    print("Check the server console for detailed debug output.")

def test_request(base_url, request_data, test_name):
    """测试单个请求"""
    print(f"🚀 Testing: {test_name}")
    
    try:
        response = requests.post(
            f"{base_url}/v1/messages",
            json=request_data,
            timeout=30
        )
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Request successful!")
            try:
                response_data = response.json()
                print(f"🆔 Response ID: {response_data.get('id', 'N/A')}")
                print(f"📋 Model: {response_data.get('model', 'N/A')}")
                
                content = response_data.get('content', [])
                if content:
                    for i, block in enumerate(content):
                        if isinstance(block, dict) and block.get('type') == 'text':
                            text = block.get('text', '')
                            print(f"🤖 Response {i+1}: {text[:100]}{'...' if len(text) > 100 else ''}")
                
            except json.JSONDecodeError:
                print("⚠️  Response is not valid JSON")
                print(f"📄 Raw response: {response.text[:200]}")
                
        elif response.status_code == 400:
            print("⚠️  Bad Request (400) - Expected for some test cases")
            print(f"📄 Error details: {response.text[:300]}")
        elif response.status_code == 500:
            print("❌ Server error (500)")
            print(f"📄 Error details: {response.text[:300]}")
            
            # 检查是否还有 JSON 解析错误
            if "JsonDecodingException" in response.text:
                print("🔍 JSON parsing error still detected!")
                if "cache_control" in response.text:
                    print("⚠️  cache_control field parsing issue")
                if "metadata" in response.text:
                    print("⚠️  metadata field parsing issue")
            else:
                print("✅ No JSON parsing errors detected")
        else:
            print(f"⚠️  Unexpected status: {response.status_code}")
            print(f"📄 Response: {response.text[:200]}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def test_invalid_json(base_url):
    """测试无效的 JSON"""
    invalid_json = '{"model": "claude-3.5-sonnet", "max_tokens": 100, "messages": [{"role": "user", "content": "test"'  # 故意不完整
    
    try:
        response = requests.post(
            f"{base_url}/v1/messages",
            data=invalid_json,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 400:
            print("✅ Invalid JSON handled correctly (400)")
        else:
            print(f"⚠️  Unexpected status for invalid JSON: {response.status_code}")
            
        print(f"📄 Response: {response.text[:200]}")
        
    except Exception as e:
        print(f"❌ Invalid JSON test error: {e}")

if __name__ == "__main__":
    print("🚀 Starting Debug Mode and Cache Control Tests")
    print("Make sure the server is running on http://localhost:8080")
    print("━" * 60)
    print("This test will generate detailed debug output on the server console.")
    print("Watch the server logs to see the debug information.")
    print("━" * 60)
    
    # 等待一下确保服务器启动
    time.sleep(2)
    
    # 运行测试
    test_debug_mode_and_cache_control()
    
    print("━" * 60)
