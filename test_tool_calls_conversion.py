#!/usr/bin/env python3

import json
import requests
import time

def test_tool_calls_conversion():
    """测试 tool_calls 响应转换为 Claude 格式"""
    print("🔄 Testing Tool Calls Response Conversion")
    print("━" * 60)
    
    base_url = "http://localhost:8080"
    
    print("🔧 OpenAI → Claude Tool Calls Conversion:")
    print("  OpenAI: {tool_calls: [{id, type, function: {name, arguments}}]}")
    print("  Claude: {content: [{type: 'tool_use', id, name, input}]}")
    print("")
    
    # 测试 1: 单个工具调用
    print("📝 Test 1: Single tool call")
    single_tool_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 150,
        "messages": [
            {
                "role": "user",
                "content": "List the files in the current directory using the LS tool."
            }
        ],
        "tools": [
            {
                "name": "LS",
                "description": "List files and directories",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "path": {
                            "type": "string",
                            "description": "Directory path to list"
                        },
                        "show_hidden": {
                            "type": "boolean",
                            "description": "Show hidden files",
                            "default": False
                        }
                    },
                    "required": ["path"]
                }
            }
        ]
    }
    
    success1, response1 = test_request_with_response(base_url, single_tool_request, "Single Tool Call")
    
    # 测试 2: 多个工具调用
    print("\n📝 Test 2: Multiple tool calls")
    multiple_tools_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 200,
        "messages": [
            {
                "role": "user",
                "content": "First list the current directory, then create a task to organize the files."
            }
        ],
        "tools": [
            {
                "name": "LS",
                "description": "List files and directories",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "Directory path"}
                    },
                    "required": ["path"]
                }
            },
            {
                "name": "CreateTask",
                "description": "Create a new task",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "title": {"type": "string", "description": "Task title"},
                        "description": {"type": "string", "description": "Task description"}
                    },
                    "required": ["title", "description"]
                }
            }
        ]
    }
    
    success2, response2 = test_request_with_response(base_url, multiple_tools_request, "Multiple Tool Calls")
    
    # 测试 3: 复杂参数的工具调用
    print("\n📝 Test 3: Complex tool parameters")
    complex_tool_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 180,
        "messages": [
            {
                "role": "user",
                "content": "Create a configuration with nested settings."
            }
        ],
        "tools": [
            {
                "name": "CreateConfig",
                "description": "Create a configuration with complex parameters",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "name": {"type": "string"},
                        "settings": {
                            "type": "object",
                            "properties": {
                                "enabled": {"type": "boolean"},
                                "level": {"type": "string", "enum": ["low", "medium", "high"]},
                                "options": {"type": "array", "items": {"type": "string"}}
                            }
                        }
                    },
                    "required": ["name", "settings"]
                }
            }
        ]
    }
    
    success3, response3 = test_request_with_response(base_url, complex_tool_request, "Complex Tool Parameters")
    
    print("\n━" * 60)
    print("📊 Tool Calls Conversion Test Results:")
    print(f"  ✅ Single tool call: {'PASS' if success1 else 'FAIL'}")
    print(f"  ✅ Multiple tool calls: {'PASS' if success2 else 'FAIL'}")
    print(f"  ✅ Complex parameters: {'PASS' if success3 else 'FAIL'}")
    
    # 分析响应格式
    print("\n🔍 Response Format Analysis:")
    if success1 and response1:
        analyze_claude_response(response1, "Single Tool Call")
    if success2 and response2:
        analyze_claude_response(response2, "Multiple Tool Calls")
    if success3 and response3:
        analyze_claude_response(response3, "Complex Parameters")
    
    all_success = success1 and success2 and success3
    
    if all_success:
        print("\n🎉 All tool calls conversion tests passed!")
        print("✅ OpenAI tool_calls → Claude tool_use conversion is working!")
        print("✅ Tool parameters are correctly parsed and converted!")
    else:
        print("\n❌ Some tool calls conversion tests failed.")
        print("Check server logs for conversion errors.")
    
    return all_success

def test_request_with_response(base_url, request_data, test_name):
    """测试单个请求，返回是否成功和响应数据"""
    print(f"🚀 Testing {test_name}...")
    
    try:
        response = requests.post(
            f"{base_url}/v1/messages",
            json=request_data,
            timeout=60
        )
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Success!")
            try:
                response_data = response.json()
                return True, response_data
            except json.JSONDecodeError:
                print("❌ Invalid JSON response")
                return False, None
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"📄 Error: {response.text[:200]}")
            return False, None
            
    except Exception as e:
        print(f"❌ Request error: {e}")
        return False, None

def analyze_claude_response(response_data, test_name):
    """分析 Claude 响应格式"""
    print(f"\n🔍 Analyzing {test_name} Response:")
    
    # 检查基本结构
    if 'content' in response_data:
        content = response_data['content']
        print(f"  📋 Content blocks: {len(content)}")
        
        for i, block in enumerate(content):
            block_type = block.get('type', 'unknown')
            print(f"    {i+1}. Type: {block_type}")
            
            if block_type == 'text':
                text = block.get('text', '')
                print(f"       Text: {text[:50]}{'...' if len(text) > 50 else ''}")
                
            elif block_type == 'tool_use':
                tool_id = block.get('id', 'no-id')
                tool_name = block.get('name', 'no-name')
                tool_input = block.get('input', {})
                print(f"       Tool: {tool_name}")
                print(f"       ID: {tool_id}")
                print(f"       Input: {json.dumps(tool_input, indent=8)}")
                
                # 验证 Claude 格式
                if tool_id and tool_name and tool_input:
                    print(f"       ✅ Valid Claude tool_use format!")
                else:
                    print(f"       ❌ Invalid Claude tool_use format!")
    
    # 检查停止原因
    stop_reason = response_data.get('stop_reason')
    if stop_reason:
        print(f"  🛑 Stop reason: {stop_reason}")
        if stop_reason == 'tool_use':
            print(f"       ✅ Correct stop reason for tool calls!")
    
    # 检查模型和使用情况
    model = response_data.get('model', 'unknown')
    usage = response_data.get('usage', {})
    print(f"  🤖 Model: {model}")
    print(f"  📊 Usage: {usage.get('input_tokens', 0)} input + {usage.get('output_tokens', 0)} output tokens")

if __name__ == "__main__":
    print("🚀 Tool Calls Response Conversion Test")
    print("Make sure the server is running on http://localhost:8080")
    print("━" * 60)
    print("This test verifies tool_calls conversion:")
    print("  ✅ OpenAI tool_calls → Claude tool_use")
    print("  ✅ Arguments JSON parsing")
    print("  ✅ Correct stop_reason mapping")
    print("  ✅ Multiple tool calls support")
    print("━" * 60)
    
    # 检查服务器状态
    try:
        response = requests.get("http://localhost:8080/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print("⚠️  Server responded but may not be healthy")
    except:
        print("❌ Server is not running. Please start it first:")
        print("   ./run_debug_test.sh")
        exit(1)
    
    print("━" * 60)
    
    # 运行测试
    success = test_tool_calls_conversion()
    
    print("━" * 60)
    if success:
        print("🎉 TOOL CALLS CONVERSION TEST PASSED!")
        print("OpenAI → Claude tool_calls conversion is working correctly!")
    else:
        print("❌ Tool calls conversion test failed.")
        print("Check server logs for conversion issues.")
