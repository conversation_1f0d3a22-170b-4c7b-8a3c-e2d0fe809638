version: '3.8'

services:
  llm-provider:
    build: .
    ports:
      - "8080:8080"
    environment:
      - PORT=8080
      - HOST=0.0.0.0
      - LOG_LEVEL=INFO
    volumes:
      # 挂载 GitHub Copilot 配置目录
      - ~/.config:/root/.config
      # 挂载日志目录
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
