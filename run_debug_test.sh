#!/bin/bash

set -e

echo "🚀 Debug Mode Test Runner"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# 检查 JAR 文件是否存在
if [ ! -f "build/libs/llm-provider.jar" ]; then
    echo "📦 Building application..."
    ./gradlew shadowJar
fi

echo "🔍 Starting server with debug mode..."
echo "The server will print detailed debug information for each request."
echo ""
echo "In another terminal, you can run:"
echo "  python3 test_debug_mode.py"
echo "  python3 test_system_field.py"
echo "  python3 test_claude_content.py"
echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "🎯 Watch for debug output below:"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# 启动服务器
java -jar build/libs/llm-provider.jar
