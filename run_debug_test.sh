#!/bin/bash

set -e

echo "🚀 GitHub Copilot Claude LLM Provider"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# 检查 JAR 文件是否存在
if [ ! -f "build/libs/llm-provider.jar" ]; then
    echo "📦 Building application..."
    ./gradlew shadowJar
fi

echo "🔍 Starting server with real GitHub Copilot integration..."
echo ""
echo "⚠️  IMPORTANT: Make sure you have:"
echo "   - Valid GitHub Copilot subscription"
echo "   - Proper authentication setup"
echo "   - Network access to GitHub Copilot API"
echo ""
echo "🧪 Available test scripts:"
echo "   python3 test_real_copilot_integration.py  # Test real AI responses"
echo "   python3 test_flexible_parsing.py          # Test JSON parsing"
echo "   python3 test_all_formats.py               # Test all API formats"
echo ""
echo "📋 Supported Claude API endpoints:"
echo "   POST /v1/messages                         # Claude Messages API"
echo "   GET  /health                              # Health check"
echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "🎯 Server starting - watch for debug output below:"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# 启动服务器
java -jar build/libs/llm-provider.jar
