#!/usr/bin/env python3

import json
import requests
import time

def test_enhanced_token_management():
    """测试增强的 token 管理功能"""
    print("🔄 Testing Enhanced GitHub Copilot API Token Management")
    print("━" * 70)
    
    base_url = "http://localhost:8080"
    
    print("🔍 Enhanced Token Management Features:")
    print("  1. ✅ Proactive refresh (5 minutes before expiry)")
    print("  2. ✅ 401 Unauthorized error detection")
    print("  3. ✅ 500 timeout error detection")
    print("  4. ✅ Automatic retry with fresh token")
    print("  5. ✅ Detailed logging for debugging")
    print("")
    
    # 测试 1: 正常请求
    print("📝 Test 1: Normal request (baseline)")
    normal_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 80,
        "messages": [
            {
                "role": "user",
                "content": "Hello! Please respond with a brief greeting."
            }
        ]
    }
    
    success1 = test_request(base_url, normal_request, "Normal Request")
    
    if not success1:
        print("❌ Baseline test failed, cannot proceed")
        return False
    
    # 测试 2: 多个连续请求（测试 token 缓存和管理）
    print("\n📝 Test 2: Multiple consecutive requests")
    print("Expected: Should use cached token efficiently")
    
    for i in range(5):
        print(f"\n🔄 Request {i+1}/5:")
        test_request_multi = {
            "model": "claude-3.5-sonnet",
            "max_tokens": 50,
            "messages": [
                {
                    "role": "user",
                    "content": f"This is request #{i+1}. Please acknowledge."
                }
            ]
        }
        
        success = test_request(base_url, test_request_multi, f"Multi-Request {i+1}")
        if not success:
            print(f"❌ Multi-request {i+1} failed")
            return False
        
        time.sleep(1)  # 短暂间隔
    
    # 测试 3: 不同模型的请求
    print("\n📝 Test 3: Different model requests")
    
    model_tests = [
        "claude-3.5-sonnet",
        "claude-3-5-haiku-20241022",
        "claude-sonnet-4"
    ]
    
    for model in model_tests:
        print(f"\n🔄 Testing model: {model}")
        model_request = {
            "model": model,
            "max_tokens": 60,
            "messages": [
                {
                    "role": "user",
                    "content": f"Hello from {model}! Please respond."
                }
            ]
        }
        
        success = test_request(base_url, model_request, f"Model {model}")
        if not success:
            print(f"❌ Model {model} test failed")
            # 继续测试其他模型
        
        time.sleep(1)
    
    # 测试 4: 复杂请求（测试在复杂场景下的 token 管理）
    print("\n📝 Test 4: Complex request with metadata")
    complex_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 120,
        "system": "You are a helpful assistant that provides concise responses.",
        "messages": [
            {
                "role": "user",
                "content": "Explain what machine learning is in one sentence."
            }
        ],
        "metadata": {
            "user_id": "enhanced_test_user",
            "session_id": "enhanced_test_session",
            "feature": "token_management_test"
        },
        "temperature": 0.7
    }
    
    success4 = test_request(base_url, complex_request, "Complex Request")
    
    print("\n━" * 70)
    print("✅ Enhanced token management test completed!")
    print("")
    print("🔍 Check server logs for token management messages:")
    print("  📋 Normal operation:")
    print("    - 'Using cached API token'")
    print("    - 'Token is valid (X minutes until expiry)'")
    print("  ⚠️  Proactive refresh:")
    print("    - 'Token expiring soon (X minutes left)'")
    print("    - 'Cached API token expiring soon, proactively refreshing...'")
    print("  🚨 Error-based refresh:")
    print("    - 'API token appears to be expired (401)'")
    print("    - 'API token appears to be expired (500 with token-related error)'")
    print("    - 'Force refreshing API token...'")
    print("    - 'Retrying with refreshed token'")
    print("  ✅ Success:")
    print("    - 'API token refreshed successfully'")
    
    return True

def test_request(base_url, request_data, test_name):
    """测试单个请求，返回是否成功"""
    try:
        response = requests.post(
            f"{base_url}/v1/messages",
            json=request_data,
            timeout=45  # 增加超时时间，考虑到可能的 token 刷新
        )
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Success!")
            try:
                response_data = response.json()
                content = response_data.get('content', [])
                if content and len(content) > 0:
                    text = content[0].get('text', '')
                    print(f"🤖 Response: {text[:60]}{'...' if len(text) > 60 else ''}")
                    return True
                else:
                    print("⚠️  No content in response")
                    return False
            except json.JSONDecodeError:
                print("❌ Invalid JSON response")
                return False
                
        elif response.status_code == 401:
            print("🔑 Authentication error (401)")
            print("💡 This should trigger automatic token refresh and retry")
            # 在新的实现中，这种情况应该被自动处理
            return False
            
        elif response.status_code == 500:
            print("❌ Server error (500)")
            error_text = response.text
            
            # 检查是否是 token 相关的 500 错误
            if any(keyword in error_text.lower() for keyword in [
                "timeout", "expired", "authentication", "unauthorized"
            ]):
                print("🔍 Token-related 500 error detected")
                print("Expected: Should trigger automatic token refresh and retry")
            
            print(f"📄 Error: {error_text[:150]}{'...' if len(error_text) > 150 else ''}")
            return False
            
        else:
            print(f"⚠️  Unexpected status: {response.status_code}")
            print(f"📄 Response: {response.text[:100]}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - make sure server is running")
        return False
    except requests.exceptions.Timeout:
        print("⏰ Request timeout (this might indicate token refresh is taking time)")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def explain_token_management_features():
    """解释增强的 token 管理功能"""
    print("\n🔍 Enhanced Token Management Features Explained:")
    print("━" * 70)
    print("1. 🕐 Proactive Refresh (5-minute buffer):")
    print("   - Checks if token expires within 5 minutes")
    print("   - Automatically refreshes before expiry")
    print("   - Prevents request failures due to expired tokens")
    print("")
    print("2. 🚨 Enhanced Error Detection:")
    print("   - 401 Unauthorized: Direct token expiry")
    print("   - 500 Internal Server Error: Timeout-related token issues")
    print("   - Keyword detection: 'timeout', 'expired', 'authentication'")
    print("")
    print("3. 🔄 Automatic Retry Logic:")
    print("   - Catches TokenExpiredException")
    print("   - Forces token refresh")
    print("   - Retries request with fresh token")
    print("   - Maximum 1 retry per request")
    print("")
    print("4. 📋 Detailed Logging:")
    print("   - Token validity checks")
    print("   - Expiry time calculations")
    print("   - Refresh triggers and results")
    print("   - Error detection and handling")

if __name__ == "__main__":
    print("🚀 Enhanced GitHub Copilot API Token Management Test")
    print("Make sure the server is running on http://localhost:8080")
    print("━" * 70)
    
    # 检查服务器状态
    try:
        response = requests.get("http://localhost:8080/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print("⚠️  Server responded but may not be healthy")
    except:
        print("❌ Server is not running. Please start it first:")
        print("   ./run_debug_test.sh")
        exit(1)
    
    # 解释功能
    explain_token_management_features()
    
    print("━" * 70)
    
    # 运行测试
    success = test_enhanced_token_management()
    
    print("━" * 70)
    if success:
        print("🎉 Enhanced token management test completed successfully!")
        print("✅ All token management features are working correctly!")
    else:
        print("❌ Some tests failed.")
        print("Check server logs for detailed token management information.")
