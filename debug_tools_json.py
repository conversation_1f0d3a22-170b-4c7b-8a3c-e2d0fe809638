#!/usr/bin/env python3

import json
import requests

def debug_tools_json():
    """调试 tools JSON 构建问题"""
    print("🔍 Debugging Tools JSON Construction")
    print("━" * 50)
    
    base_url = "http://localhost:8080"
    
    # 简单的工具测试请求
    test_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 100,
        "messages": [
            {
                "role": "user",
                "content": "Use the calculator tool to compute 2 + 2."
            }
        ],
        "tools": [
            {
                "type": "function",
                "function": {
                    "name": "calculator",
                    "description": "Perform basic math calculations",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "expression": {
                                "type": "string",
                                "description": "Math expression to evaluate"
                            }
                        },
                        "required": ["expression"]
                    }
                }
            }
        ],
        "tool_choice": "auto"
    }
    
    print("📤 Sending request with tools:")
    print(json.dumps(test_request, indent=2))
    print("━" * 50)
    
    try:
        response = requests.post(
            f"{base_url}/v1/messages",
            json=test_request,
            timeout=30
        )
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Request successful!")
            response_data = response.json()
            print("📥 Response preview:")
            print(json.dumps(response_data, indent=2)[:500] + "...")
        else:
            print("❌ Request failed!")
            print(f"📄 Error response: {response.text[:300]}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("━" * 50)
    print("🔍 Check server console for:")
    print("  1. 'Request Body JSON:' section")
    print("  2. Look for 'tools' field in the JSON")
    print("  3. Check if tools are properly serialized")

if __name__ == "__main__":
    print("🚀 Tools JSON Debug Test")
    print("This will help identify why tools aren't appearing in the request JSON")
    print("━" * 50)
    
    # 检查服务器状态
    try:
        response = requests.get("http://localhost:8080/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print("⚠️  Server may not be healthy")
    except:
        print("❌ Server is not running. Please start it first:")
        print("   ./run_debug_test.sh")
        exit(1)
    
    print("━" * 50)
    debug_tools_json()
