#!/usr/bin/env kotlin

import java.io.File

/**
 * 简单的测试脚本来验证 GitHub Copilot 配置检查功能
 */

fun main() {
    println("🔍 Testing GitHub Copilot Configuration Check")
    println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
    
    val homeDir = System.getProperty("user.home")
    val configPath = "$homeDir/.config/github-copilot/app.json"
    val configFile = File(configPath)
    
    println("📁 Home directory: $homeDir")
    println("📄 Config path: $configPath")
    println("📋 Config file exists: ${configFile.exists()}")
    
    if (configFile.exists()) {
        println("✅ GitHub Copilot config file found!")
        println("📏 File size: ${configFile.length()} bytes")
        
        try {
            val content = configFile.readText()
            println("📝 Config content preview:")
            println(content.take(200) + if (content.length > 200) "..." else "")
            
            // 简单的 OAuth token 提取
            val oauthTokenRegex = """"oauth_token":\s*"([^"]+)"""".toRegex()
            val matches = oauthTokenRegex.findAll(content)
            
            println("\n🔑 Found OAuth tokens:")
            matches.forEach { match ->
                val token = match.groupValues[1]
                println("   - ${token.take(10)}...${token.takeLast(10)} (${token.length} chars)")
            }
            
            // 检查目标客户端 ID
            val targetClientId = "Iv23ctfURkiMfJ4xr5mv"
            if (content.contains(targetClientId)) {
                println("🎯 Target client ID ($targetClientId) found in config!")
            } else {
                println("⚠️  Target client ID ($targetClientId) not found in config")
            }
            
        } catch (e: Exception) {
            println("❌ Error reading config file: ${e.message}")
        }
    } else {
        println("❌ GitHub Copilot config file not found")
        println("💡 You may need to:")
        println("   1. Install GitHub Copilot CLI")
        println("   2. Run 'gh auth login' or similar")
        println("   3. Or use the device auth flow in our application")
    }
    
    println("\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
    println("✨ Test completed!")
}
