#!/usr/bin/env python3

import json
import requests

def quick_test():
    """快速测试修复是否有效"""
    print("⚡ Quick Test - GitHub Copilot Claude LLM Provider")
    print("━" * 50)
    
    base_url = "http://localhost:8080"
    
    # 简单测试请求
    test_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 50,
        "messages": [
            {
                "role": "user",
                "content": "Say hello in a friendly way."
            }
        ]
    }
    
    try:
        print("🚀 Sending test request...")
        response = requests.post(
            f"{base_url}/v1/messages",
            json=test_request,
            timeout=30
        )
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ SUCCESS! Request completed successfully!")
            try:
                response_data = response.json()
                content = response_data.get('content', [])
                if content and len(content) > 0:
                    text = content[0].get('text', '')
                    print(f"🤖 AI Response: {text}")
                    print("✅ Real AI response received!")
                    print("🎉 All JSON parsing issues have been resolved!")
                    return True
                else:
                    print("⚠️  No content in response")
                    return False
            except json.JSONDecodeError:
                print("❌ Invalid JSON response")
                return False
                
        elif response.status_code == 500:
            print("❌ Server error (500)")
            error_text = response.text
            if "JsonDecodingException" in error_text or "JsonConvertException" in error_text:
                print("🔍 JSON parsing error still detected!")
                print("Check the error details:")
                print(error_text[:300])
                return False
            else:
                print("⚠️  Other server error")
                return False
        else:
            print(f"⚠️  Unexpected status: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed")
        print("Make sure the server is running: ./run_debug_test.sh")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("⚡ Quick Test for GitHub Copilot Claude LLM Provider")
    print("Make sure the server is running on http://localhost:8080")
    print("━" * 50)
    
    success = quick_test()
    
    print("━" * 50)
    if success:
        print("🎉 QUICK TEST PASSED!")
        print("GitHub Copilot Claude LLM Provider is working correctly!")
    else:
        print("❌ Quick test failed.")
        print("Check server logs for more details.")
