#!/bin/bash

set -e

echo "🐳 Docker Build and Test Script"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# 1. 构建应用
echo "📦 Building application..."
./gradlew shadowJar

# 2. 构建 Docker 镜像
echo "🔨 Building Docker image..."
docker build -t github-copilot-llm-provider .

# 3. 运行容器（后台）
echo "🚀 Starting Docker container..."
docker run -d \
  --name llm-provider-test \
  -p 8080:8080 \
  -v ~/.config:/root/.config \
  github-copilot-llm-provider

# 4. 等待容器启动
echo "⏳ Waiting for container to start..."
sleep 10

# 5. 测试健康检查
echo "🏥 Testing health check..."
for i in {1..10}; do
  if curl -f http://localhost:8080/health > /dev/null 2>&1; then
    echo "✅ Health check passed!"
    break
  else
    echo "⏳ Waiting for health check... ($i/10)"
    sleep 3
  fi
  
  if [ $i -eq 10 ]; then
    echo "❌ Health check failed after 30 seconds"
    docker logs llm-provider-test
    docker stop llm-provider-test
    docker rm llm-provider-test
    exit 1
  fi
done

# 6. 测试 API 端点
echo "🧪 Testing API endpoints..."

# 测试根端点
echo "📋 Testing root endpoint..."
if curl -f http://localhost:8080/ > /dev/null 2>&1; then
  echo "✅ Root endpoint working"
else
  echo "❌ Root endpoint failed"
fi

# 测试 OpenAI API
echo "🤖 Testing OpenAI API endpoint..."
response=$(curl -s -o /dev/null -w "%{http_code}" \
  -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{"model":"gpt-4","messages":[{"role":"user","content":"test"}]}')

if [ "$response" != "500" ]; then
  echo "✅ OpenAI API endpoint working (status: $response)"
else
  echo "❌ OpenAI API endpoint returned 500 error"
fi

# 测试 Claude API
echo "🧠 Testing Claude API endpoint..."
response=$(curl -s -o /dev/null -w "%{http_code}" \
  -X POST http://localhost:8080/v1/messages \
  -H "Content-Type: application/json" \
  -d '{"model":"claude-3.5-sonnet","max_tokens":100,"messages":[{"role":"user","content":"test"}]}')

if [ "$response" != "500" ]; then
  echo "✅ Claude API endpoint working (status: $response)"
else
  echo "❌ Claude API endpoint returned 500 error"
fi

# 7. 显示容器日志
echo "📋 Container logs (last 20 lines):"
docker logs --tail 20 llm-provider-test

# 8. 清理
echo "🧹 Cleaning up..."
docker stop llm-provider-test
docker rm llm-provider-test

echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "🎉 Docker test completed successfully!"
echo "💡 To run the container manually:"
echo "   docker run -p 8080:8080 -v ~/.config:/root/.config github-copilot-llm-provider"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
