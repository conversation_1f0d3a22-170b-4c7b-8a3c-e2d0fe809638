#!/usr/bin/env python3

import json
import requests
import time
import sys

def test_flexible_parsing():
    """测试灵活的 Try-Catch 解析方法"""
    print("🔄 Testing Flexible Try-Catch Parsing Method")
    print("━" * 60)
    
    base_url = "http://localhost:8080"
    
    tests = [
        # 测试 1: 最简单的字符串格式
        {
            "name": "Simple String Format",
            "request": {
                "model": "claude-3.5-sonnet",
                "max_tokens": 50,
                "messages": [
                    {
                        "role": "user",
                        "content": "Hello world"
                    }
                ]
            }
        },
        
        # 测试 2: 实际错误格式 1（简单字符串）
        {
            "name": "Real Error Format 1 (Simple String)",
            "request": {
                "model": "claude-3-5-haiku-20241022",
                "max_tokens": 1,
                "messages": [
                    {
                        "role": "user",
                        "content": "test"
                    }
                ],
                "temperature": 0,
                "metadata": {
                    "user_id": "8829af087e827e3a455e1326c6d042f6959c1512ee00d1914648e6c1ed201d61"
                }
            }
        },
        
        # 测试 3: 复杂数组格式
        {
            "name": "Complex Array Format",
            "request": {
                "model": "claude-sonnet-4-20250514",
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "Hello, how are you?"
                            },
                            {
                                "type": "text",
                                "text": "Please help me with this task.",
                                "cache_control": {
                                    "type": "ephemeral"
                                }
                            }
                        ]
                    }
                ],
                "temperature": 1,
                "system": [
                    {
                        "type": "text",
                        "text": "You are a helpful assistant.",
                        "cache_control": {
                            "type": "ephemeral"
                        }
                    }
                ],
                "metadata": {
                    "user_id": "8829af087e827e3a455e1326c6d042f6959c1512ee00d1914648e6c1ed201d61"
                },
                "max_tokens": 100
            }
        },
        
        # 测试 4: 混合格式（system 字符串，content 数组）
        {
            "name": "Mixed Format (String System, Array Content)",
            "request": {
                "model": "claude-3.5-sonnet",
                "max_tokens": 100,
                "system": "You are a helpful assistant.",  # 字符串
                "messages": [
                    {
                        "role": "user",
                        "content": [  # 数组
                            {
                                "type": "text",
                                "text": "What is AI?"
                            }
                        ]
                    }
                ],
                "temperature": 0.7
            }
        },
        
        # 测试 5: 混合格式（system 数组，content 字符串）
        {
            "name": "Mixed Format (Array System, String Content)",
            "request": {
                "model": "claude-3.5-sonnet",
                "max_tokens": 100,
                "system": [  # 数组
                    {
                        "type": "text",
                        "text": "You are helpful."
                    }
                ],
                "messages": [
                    {
                        "role": "user",
                        "content": "What is the weather?"  # 字符串
                    }
                ],
                "temperature": 0.7
            }
        },
        
        # 测试 6: 带工具的复杂请求
        {
            "name": "Complex Request with Tools",
            "request": {
                "model": "claude-3.5-sonnet",
                "max_tokens": 200,
                "messages": [
                    {
                        "role": "user",
                        "content": "Use the calculator tool to compute 2+2"
                    }
                ],
                "tools": [
                    {
                        "name": "calculator",
                        "description": "Perform basic arithmetic",
                        "input_schema": {
                            "type": "object",
                            "properties": {
                                "expression": {
                                    "type": "string",
                                    "description": "Mathematical expression"
                                }
                            },
                            "required": ["expression"]
                        }
                    }
                ],
                "cache_control": {
                    "type": "ephemeral"
                },
                "metadata": {
                    "test_case": "tools_test",
                    "version": "1.0"
                }
            }
        },
        
        # 测试 7: 边界情况（空内容）
        {
            "name": "Edge Case (Empty Content)",
            "request": {
                "model": "claude-3.5-sonnet",
                "max_tokens": 50,
                "messages": [
                    {
                        "role": "user",
                        "content": ""
                    }
                ]
            }
        }
    ]
    
    success_count = 0
    total_count = len(tests)
    
    for i, test in enumerate(tests, 1):
        print(f"\n📝 Test {i}/{total_count}: {test['name']}")
        print("─" * 50)
        
        success = test_request(base_url, test['request'], test['name'])
        if success:
            success_count += 1
        
        time.sleep(1)  # 避免请求过快
    
    print(f"\n━" * 60)
    print(f"📊 Test Results: {success_count}/{total_count} tests passed")
    
    if success_count == total_count:
        print("🎉 All tests passed! Flexible Try-Catch parsing is working correctly.")
        return True
    else:
        print(f"⚠️  {total_count - success_count} tests failed. Check server logs for details.")
        return False

def test_request(base_url, request_data, test_name):
    """测试单个请求，返回是否成功"""
    try:
        response = requests.post(
            f"{base_url}/v1/messages",
            json=request_data,
            timeout=30
        )
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Success!")
            try:
                response_data = response.json()
                content = response_data.get('content', [])
                if content and isinstance(content, list) and len(content) > 0:
                    first_block = content[0]
                    if isinstance(first_block, dict) and first_block.get('type') == 'text':
                        text = first_block.get('text', '')
                        print(f"🤖 Response: {text[:50]}{'...' if len(text) > 50 else ''}")
                return True
            except json.JSONDecodeError:
                print("⚠️  Response not JSON, but request was accepted")
                return True
                
        elif response.status_code in [400, 401, 403]:
            print(f"⚠️  Client error ({response.status_code}) - may be expected")
            return True  # 客户端错误可能是预期的
            
        elif response.status_code == 500:
            print("❌ Server error (500)")
            error_text = response.text[:500]
            print(f"📄 Error: {error_text}")
            
            # 检查是否是我们要修复的序列化错误
            if any(keyword in error_text for keyword in [
                "JsonDecodingException", 
                "SerializationException",
                "sealed class",
                "discriminator",
                "type.*conflicts"
            ]):
                print("🔍 Serialization error detected - this should be fixed!")
                return False
            else:
                print("⚠️  Other server error - may not be related to parsing")
                return True
        else:
            print(f"⚠️  Unexpected status: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - make sure server is running")
        return False
    except requests.exceptions.Timeout:
        print("⏰ Request timeout")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Flexible Try-Catch Parsing Tests")
    print("Make sure the server is running on http://localhost:8080")
    print("━" * 60)
    
    # 检查服务器是否运行
    try:
        response = requests.get("http://localhost:8080/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print("⚠️  Server responded but may not be healthy")
    except:
        print("❌ Server is not running. Please start it first:")
        print("   ./run_debug_test.sh")
        sys.exit(1)
    
    print("━" * 60)
    print("This test validates the flexible Try-Catch parsing approach:")
    print("- Try parsing as string first")
    print("- If that fails, try parsing as array")
    print("- Graceful fallback for any parsing errors")
    print("━" * 60)
    
    # 运行测试
    success = test_flexible_parsing()
    
    print("━" * 60)
    if success:
        print("🎉 All flexible parsing tests completed successfully!")
        print("The Try-Catch approach is working correctly for all formats!")
        sys.exit(0)
    else:
        print("❌ Some tests failed. Check the output above.")
        sys.exit(1)
