#!/usr/bin/env python3

import json
import requests
import time
from pathlib import Path

def test_github_copilot_integration():
    """端到端测试 GitHub Copilot 集成"""
    print("🚀 End-to-End GitHub Copilot Integration Test")
    print("━" * 60)
    
    # 1. 检查配置
    home_dir = Path.home()
    config_path = home_dir / ".config" / "github-copilot" / "apps.json"
    
    if not config_path.exists():
        print("❌ GitHub Copilot config not found")
        return False
    
    with open(config_path, 'r') as f:
        config_data = json.load(f)
    
    target_client_id = "Iv23ctfURkiMfJ4xr5mv"
    target_key = f"github.com:{target_client_id}"
    
    if target_key not in config_data:
        print(f"❌ Target client ID ({target_client_id}) not found")
        return False
    
    oauth_token = config_data[target_key]["oauth_token"]
    user = config_data[target_key]["user"]
    
    print(f"✅ OAuth token found for user: {user}")
    
    # 2. 获取 API token
    print("📡 Getting API token...")
    try:
        response = requests.get(
            "https://api.github.com/copilot_internal/v2/token",
            headers={
                "Authorization": f"token {oauth_token}",
                "Accept": "application/json",
                "User-Agent": "GitHub-Copilot-LLM-Provider/1.0"
            }
        )
        
        if response.status_code != 200:
            print(f"❌ Failed to get API token: {response.status_code}")
            return False
        
        api_token_data = response.json()
        api_token = api_token_data["token"]
        api_endpoint = api_token_data.get("endpoints", {}).get("api", "https://api.individual.githubcopilot.com")
        print(f"✅ API token obtained")
        print(f"🌐 API endpoint: {api_endpoint}")
        
    except Exception as e:
        print(f"❌ Error getting API token: {e}")
        return False
    
    # 3. 获取支持的模型
    print("📋 Getting supported models...")
    try:
        models_response = requests.get(
            f"{api_endpoint}/models",
            headers={
                "Authorization": f"Bearer {api_token}",
                "Accept": "application/json",
                "User-Agent": "GitHub-Copilot-LLM-Provider/1.0",
                "Editor-Version": "vscode/1.95.0",
                "Editor-Plugin-Version": "copilot/1.0.0"
            }
        )
        
        if models_response.status_code != 200:
            print(f"❌ Failed to get models: {models_response.status_code}")
            return False
        
        models_data = models_response.json()
        models = models_data.get("data", [])
        
        # 查找 Claude 模型
        claude_models = [m for m in models if 'claude' in m['id'].lower()]
        if not claude_models:
            print("❌ No Claude models found")
            return False
        
        # 选择最佳 Claude 模型
        preferred_models = ["claude-sonnet-4", "claude-3.7-sonnet", "claude-3.5-sonnet"]
        selected_model = None
        
        for preferred in preferred_models:
            for model in claude_models:
                if model['id'] == preferred:
                    selected_model = preferred
                    break
            if selected_model:
                break
        
        if not selected_model:
            selected_model = claude_models[0]['id']
        
        print(f"✅ Selected Claude model: {selected_model}")
        
    except Exception as e:
        print(f"❌ Error getting models: {e}")
        return False
    
    # 4. 测试聊天完成
    print("💬 Testing chat completion...")
    try:
        chat_request = {
            "model": selected_model,
            "messages": [
                {
                    "role": "user",
                    "content": "Hello! Please respond with exactly: 'GitHub Copilot integration test successful!'"
                }
            ],
            "stream": False,
            "max_tokens": 100
        }
        
        chat_response = requests.post(
            f"{api_endpoint}/chat/completions",
            headers={
                "Authorization": f"Bearer {api_token}",
                "Content-Type": "application/json",
                "Accept": "application/json",
                "User-Agent": "GitHub-Copilot-LLM-Provider/1.0",
                "Editor-Version": "vscode/1.95.0",
                "Editor-Plugin-Version": "copilot/1.0.0"
            },
            json=chat_request
        )
        
        if chat_response.status_code != 200:
            print(f"❌ Chat completion failed: {chat_response.status_code}")
            print(f"Response: {chat_response.text}")
            return False
        
        chat_data = chat_response.json()
        if "choices" not in chat_data or not chat_data["choices"]:
            print("❌ No choices in chat response")
            return False
        
        response_content = chat_data["choices"][0]["message"]["content"]
        print(f"✅ Chat completion successful!")
        print(f"🤖 Response: {response_content}")
        
        # 验证响应内容
        if "integration test successful" in response_content.lower():
            print("🎉 Response validation passed!")
        else:
            print("⚠️  Response validation failed, but API is working")
        
    except Exception as e:
        print(f"❌ Error in chat completion: {e}")
        return False
    
    # 5. 测试流式响应
    print("\n🌊 Testing streaming chat completion...")
    try:
        stream_request = {
            "model": selected_model,
            "messages": [
                {
                    "role": "user",
                    "content": "Count from 1 to 3, one number per response chunk."
                }
            ],
            "stream": True,
            "max_tokens": 50
        }
        
        stream_response = requests.post(
            f"{api_endpoint}/chat/completions",
            headers={
                "Authorization": f"Bearer {api_token}",
                "Content-Type": "application/json",
                "Accept": "text/event-stream",
                "User-Agent": "GitHub-Copilot-LLM-Provider/1.0",
                "Editor-Version": "vscode/1.95.0",
                "Editor-Plugin-Version": "copilot/1.0.0"
            },
            json=stream_request,
            stream=True
        )
        
        if stream_response.status_code != 200:
            print(f"❌ Streaming failed: {stream_response.status_code}")
            return False
        
        print("✅ Streaming response received:")
        chunk_count = 0
        for line in stream_response.iter_lines():
            if line:
                line_str = line.decode('utf-8')
                if line_str.startswith('data: ') and line_str != 'data: [DONE]':
                    chunk_count += 1
                    if chunk_count <= 5:  # 只显示前5个块
                        data = line_str[6:]  # 移除 'data: ' 前缀
                        try:
                            chunk_data = json.loads(data)
                            if "choices" in chunk_data and chunk_data["choices"]:
                                delta = chunk_data["choices"][0].get("delta", {})
                                content = delta.get("content", "")
                                if content:
                                    print(f"   📦 Chunk {chunk_count}: {repr(content)}")
                        except:
                            pass
        
        print(f"✅ Received {chunk_count} streaming chunks")
        
    except Exception as e:
        print(f"❌ Error in streaming: {e}")
        return False
    
    print("\n🎉 All tests passed! GitHub Copilot integration is working perfectly!")
    print("━" * 60)
    print("📊 Test Summary:")
    print(f"   🔑 OAuth Token: ✅ Valid")
    print(f"   🎫 API Token: ✅ Obtained")
    print(f"   📋 Models: ✅ {len(models)} available")
    print(f"   🤖 Preferred Model: ✅ {selected_model}")
    print(f"   💬 Chat Completion: ✅ Working")
    print(f"   🌊 Streaming: ✅ Working")
    print("━" * 60)
    
    return True

if __name__ == "__main__":
    success = test_github_copilot_integration()
    exit(0 if success else 1)
