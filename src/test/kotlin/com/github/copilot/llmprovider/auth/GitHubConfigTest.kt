package com.github.copilot.llmprovider.auth

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.io.TempDir
import java.io.File
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * 测试 GitHub Copilot 配置管理
 * 
 * 测试理由：
 * - 验证配置文件的读取和解析
 * - 确保 OAuth token 的正确提取
 * - 测试配置文件的保存功能
 * - 验证错误处理和边界情况
 */
class GitHubConfigTest {

    @Test
    fun `should check if GitHub Copilot config exists`(@TempDir tempDir: File) {
        // Arrange
        val configManager = GitHubConfigManager(tempDir.absolutePath)
        
        // Act & Assert
        assertFalse(configManager.hasExistingConfig())
    }

    @Test
    fun `should parse existing GitHub Copilot config`(@TempDir tempDir: File) {
        // Arrange
        val configFile = File(tempDir, "github-copilot/app.json")
        configFile.parentFile.mkdirs()
        configFile.writeText("""
            {
                "github.com:Iv23ctfURkiMfJ4xr5mv": {
                    "oauth_token": "****************************************",
                    "user": "iptton",
                    "githubAppId": "Iv23ctfURkiMfJ4xr5mv"
                },
                "github.com:Iv1.b507a08c87ecfe98": {
                    "user": "iptton",
                    "oauth_token": "****************************************",
                    "githubAppId": "Iv1.b507a08c87ecfe98"
                }
            }
        """.trimIndent())
        
        val configManager = GitHubConfigManager(tempDir.absolutePath)
        
        // Act
        val oauthToken = configManager.getExistingOAuthToken()
        
        // Assert
        assertNotNull(oauthToken)
        assertTrue(oauthToken.startsWith("ghu_"))
    }

    @Test
    fun `should return null for non-existent config`(@TempDir tempDir: File) {
        // Arrange
        val configManager = GitHubConfigManager(tempDir.absolutePath)
        
        // Act
        val oauthToken = configManager.getExistingOAuthToken()
        
        // Assert
        assertNull(oauthToken)
    }

    @Test
    fun `should save OAuth token to config`(@TempDir tempDir: File) {
        // Arrange
        val configManager = GitHubConfigManager(tempDir.absolutePath)
        val testToken = "ghu_testToken123"
        val testUser = "testuser"
        
        // Act
        configManager.saveOAuthToken(testToken, testUser)
        
        // Assert
        val savedToken = configManager.getExistingOAuthToken()
        assertEquals(testToken, savedToken)
    }

    @Test
    fun `should handle malformed config file gracefully`(@TempDir tempDir: File) {
        // Arrange
        val configFile = File(tempDir, "github-copilot/app.json")
        configFile.parentFile.mkdirs()
        configFile.writeText("invalid json")
        
        val configManager = GitHubConfigManager(tempDir.absolutePath)
        
        // Act
        val oauthToken = configManager.getExistingOAuthToken()
        
        // Assert
        assertNull(oauthToken)
    }

    @Test
    fun `should extract correct OAuth token for target client ID`(@TempDir tempDir: File) {
        // Arrange
        val configFile = File(tempDir, "github-copilot/app.json")
        configFile.parentFile.mkdirs()
        configFile.writeText("""
            {
                "github.com:Iv23ctfURkiMfJ4xr5mv": {
                    "oauth_token": "ghu_targetToken",
                    "user": "iptton",
                    "githubAppId": "Iv23ctfURkiMfJ4xr5mv"
                },
                "github.com:other": {
                    "oauth_token": "ghu_otherToken",
                    "user": "iptton",
                    "githubAppId": "other"
                }
            }
        """.trimIndent())
        
        val configManager = GitHubConfigManager(tempDir.absolutePath)
        
        // Act
        val oauthToken = configManager.getExistingOAuthToken()
        
        // Assert
        assertEquals("ghu_targetToken", oauthToken)
    }

    @Test
    fun `should update existing config when saving new token`(@TempDir tempDir: File) {
        // Arrange
        val configFile = File(tempDir, "github-copilot/app.json")
        configFile.parentFile.mkdirs()
        configFile.writeText("""
            {
                "github.com:other": {
                    "oauth_token": "ghu_otherToken",
                    "user": "other",
                    "githubAppId": "other"
                }
            }
        """.trimIndent())
        
        val configManager = GitHubConfigManager(tempDir.absolutePath)
        
        // Act
        configManager.saveOAuthToken("ghu_newToken", "newuser")
        
        // Assert
        val savedToken = configManager.getExistingOAuthToken()
        assertEquals("ghu_newToken", savedToken)
        
        // Verify other entries are preserved
        val configContent = configFile.readText()
        assertTrue(configContent.contains("ghu_otherToken"))
    }
}
