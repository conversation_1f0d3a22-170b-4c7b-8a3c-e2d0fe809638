package com.github.copilot.llmprovider.util

import com.github.copilot.llmprovider.model.*
import mu.KotlinLogging

/**
 * 请求响应日志格式化工具
 */
object RequestResponseLogger {
    private val logger = KotlinLogging.logger {}

    /**
     * 记录 OpenAI 聊天完成请求
     */
    fun logOpenAIRequest(request: OpenAIChatCompletionRequest) {
        val formattedRequest = buildString {
            appendLine("🤖 OpenAI Chat Completion Request")
            appendLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
            appendLine("📋 Model: ${request.model}")
            appendLine("🌡️  Temperature: ${request.temperature ?: "default"}")
            appendLine("🎯 Max Tokens: ${request.maxTokens ?: "default"}")
            appendLine("🔄 Stream: ${request.stream}")
            appendLine()
            appendLine("💬 Messages:")
            request.messages.forEachIndexed { index, message ->
                appendLine("  ${index + 1}. ${formatRole(message.role)}: ${message.content}")
            }
            appendLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
        }
        
        logger.info { formattedRequest }
        println(formattedRequest)
    }

    /**
     * 记录 Claude 消息请求
     */
    fun logClaudeRequest(request: ClaudeMessageRequest) {
        val formattedRequest = buildString {
            appendLine("🧠 Claude Message Request")
            appendLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
            appendLine("📋 Model: ${request.model}")
            appendLine("🌡️  Temperature: ${request.temperature ?: "default"}")
            appendLine("🎯 Max Tokens: ${request.maxTokens}")
            appendLine("🔄 Stream: ${request.stream}")
            
            request.system?.let { systemContent ->
                val systemText = ClaudeContentHelper.extractText(systemContent)
                if (systemText.isNotBlank()) {
                    appendLine("🔧 System: $systemText")
                }
            }
            
            appendLine()
            appendLine("💬 Messages:")
            request.messages.forEachIndexed { index, message ->
                val content = ClaudeContentHelper.extractText(message.content)
                appendLine("  ${index + 1}. ${formatRole(message.role)}: $content")
            }
            appendLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
        }
        
        logger.info { formattedRequest }
        println(formattedRequest)
    }

    /**
     * 记录 OpenAI 聊天完成响应
     */
    fun logOpenAIResponse(response: OpenAIChatCompletionResponse) {
        val formattedResponse = buildString {
            appendLine("✅ OpenAI Chat Completion Response")
            appendLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
            appendLine("🆔 ID: ${response.id}")
            appendLine("📋 Model: ${response.model}")
            appendLine("📊 Usage: ${response.usage?.promptTokens ?: 0} prompt + ${response.usage?.completionTokens ?: 0} completion = ${response.usage?.totalTokens ?: 0} total tokens")
            appendLine()
            
            if (response.choices.isNotEmpty()) {
                appendLine("🤖 Assistant Response:")
                response.choices.forEachIndexed { index, choice ->
                    val content = choice.message?.content ?: choice.delta?.content ?: ""
                    if (content.isNotBlank()) {
                        appendLine("  ${index + 1}. ${formatRole("assistant")}: $content")
                    }
                    choice.finishReason?.let { reason ->
                        appendLine("     🏁 Finish Reason: $reason")
                    }
                }
            }
            appendLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
        }
        
        logger.info { formattedResponse }
        println(formattedResponse)
    }

    /**
     * 记录 Claude 消息响应
     */
    fun logClaudeResponse(response: ClaudeMessageResponse) {
        val formattedResponse = buildString {
            appendLine("✅ Claude Message Response")
            appendLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
            appendLine("🆔 ID: ${response.id}")
            appendLine("📋 Model: ${response.model}")
            appendLine("📊 Usage: ${response.usage.inputTokens} input + ${response.usage.outputTokens} output tokens")
            appendLine("🏁 Stop Reason: ${response.stopReason}")
            appendLine()
            
            if (response.content.isNotEmpty()) {
                appendLine("🧠 Claude Response:")
                response.content.forEachIndexed { index, block ->
                    when (block) {
                        is ClaudeContentBlock.Text -> {
                            appendLine("  ${index + 1}. ${formatRole("assistant")}: ${block.text}")
                        }
                        is ClaudeContentBlock.ToolUse -> {
                            appendLine("  ${index + 1}. 🔧 Tool Use: ${block.name}")
                        }
                        is ClaudeContentBlock.ToolResult -> {
                            appendLine("  ${index + 1}. 📋 Tool Result: ${block.content}")
                        }
                    }
                }
            }
            appendLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
        }
        
        logger.info { formattedResponse }
        println(formattedResponse)
    }

    /**
     * 记录流式响应块
     */
    fun logStreamChunk(chunk: String, isFirst: Boolean = false, isLast: Boolean = false) {
        if (isFirst) {
            val header = buildString {
                appendLine("🌊 Streaming Response Started")
                appendLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
            }
            logger.info { header }
            println(header)
        }
        
        if (chunk.isNotBlank() && chunk != "[DONE]") {
            val formattedChunk = "📦 Chunk: $chunk"
            logger.debug { formattedChunk }
            print(chunk) // 实时显示内容
        }
        
        if (isLast) {
            val footer = buildString {
                appendLine()
                appendLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
                appendLine("🏁 Streaming Response Completed")
            }
            logger.info { footer }
            println(footer)
        }
    }

    /**
     * 记录错误
     */
    fun logError(error: String, details: String? = null) {
        val formattedError = buildString {
            appendLine("❌ Error Occurred")
            appendLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
            appendLine("🚨 Error: $error")
            details?.let {
                appendLine("📋 Details: $it")
            }
            appendLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
        }
        
        logger.error { formattedError }
        println(formattedError)
    }

    /**
     * 格式化角色显示
     */
    private fun formatRole(role: String): String {
        return when (role.lowercase()) {
            "user" -> "👤 User"
            "assistant" -> "🤖 Assistant"
            "system" -> "🔧 System"
            "tool" -> "🛠️  Tool"
            else -> "❓ $role"
        }
    }
}
