package com.github.copilot.llmprovider.model

import kotlinx.serialization.*
import kotlinx.serialization.builtins.*
import kotlinx.serialization.descriptors.*
import kotlinx.serialization.encoding.*
import kotlinx.serialization.json.*

/**
 * Claude Message Request
 */
@Serializable
data class ClaudeMessageRequest(
    val model: String,
    @SerialName("max_tokens")
    val maxTokens: Int,
    val messages: List<ClaudeMessage>,
    val system: ClaudeContent? = null,
    val stream: Boolean = false,
    val temperature: Double? = null,
    @SerialName("top_p")
    val topP: Double? = null,
    @SerialName("top_k")
    val topK: Int? = null,
    val tools: List<ClaudeTool>? = null,
    @SerialName("tool_choice")
    val toolChoice: JsonElement? = null,
    @SerialName("stop_sequences")
    val stopSequences: List<String>? = null,
    @SerialName("cache_control")
    val cacheControl: JsonElement? = null,
    val metadata: JsonElement? = null
)

/**
 * Claude Message
 */
@Serializable
data class ClaudeMessage(
    val role: String,
    val content: ClaudeContent,
    @SerialName("cache_control")
    val cacheControl: JsonElement? = null
)

/**
 * Claude Content - 支持字符串或内容块数组
 */
@Serializable(with = ClaudeContentSerializer::class)
sealed class ClaudeContent {
    @Serializable
    data class Text(val text: String) : ClaudeContent()

    @Serializable
    data class Blocks(val blocks: List<ClaudeContentBlock>) : ClaudeContent()
}

/**
 * Claude Content 序列化器
 */
object ClaudeContentSerializer : KSerializer<ClaudeContent> {
    override val descriptor: SerialDescriptor = JsonElement.serializer().descriptor

    override fun serialize(encoder: Encoder, value: ClaudeContent) {
        when (value) {
            is ClaudeContent.Text -> encoder.encodeString(value.text)
            is ClaudeContent.Blocks -> encoder.encodeSerializableValue(
                ListSerializer(ClaudeContentBlock.serializer()),
                value.blocks
            )
        }
    }

    override fun deserialize(decoder: Decoder): ClaudeContent {
        val element = decoder.decodeSerializableValue(JsonElement.serializer())
        return when (element) {
            is JsonPrimitive -> ClaudeContent.Text(element.content)
            is JsonArray -> ClaudeContent.Blocks(
                Json.decodeFromJsonElement(ListSerializer(ClaudeContentBlock.serializer()), element)
            )
            else -> throw SerializationException("Unexpected JSON element for ClaudeContent: $element")
        }
    }
}

/**
 * Claude Content Block - 支持不同类型的内容块
 */
@Serializable
sealed class ClaudeContentBlock {
    abstract val type: String

    @Serializable
    @SerialName("text")
    data class Text(
        override val type: String = "text",
        val text: String,
        @SerialName("cache_control")
        val cacheControl: JsonElement? = null
    ) : ClaudeContentBlock()

    @Serializable
    @SerialName("tool_use")
    data class ToolUse(
        override val type: String = "tool_use",
        val id: String,
        val name: String,
        val input: Map<String, JsonElement>,
        @SerialName("cache_control")
        val cacheControl: JsonElement? = null
    ) : ClaudeContentBlock()

    @Serializable
    @SerialName("tool_result")
    data class ToolResult(
        override val type: String = "tool_result",
        @SerialName("tool_use_id")
        val toolUseId: String,
        val content: String,
        @SerialName("is_error")
        val isError: Boolean = false,
        @SerialName("cache_control")
        val cacheControl: JsonElement? = null
    ) : ClaudeContentBlock()
}

/**
 * Claude Tool
 */
@Serializable
data class ClaudeTool(
    val name: String,
    val description: String,
    @SerialName("input_schema")
    val inputSchema: JsonElement
)

/**
 * Claude Message Response
 */
@Serializable
data class ClaudeMessageResponse(
    val id: String,
    val type: String,
    val role: String,
    val content: List<ClaudeContentBlock>,
    val model: String,
    @SerialName("stop_reason")
    val stopReason: String? = null,
    @SerialName("stop_sequence")
    val stopSequence: String? = null,
    val usage: ClaudeUsage
)

/**
 * Claude Usage
 */
@Serializable
data class ClaudeUsage(
    @SerialName("input_tokens")
    val inputTokens: Int,
    @SerialName("output_tokens")
    val outputTokens: Int
)

/**
 * Claude Error Response
 */
@Serializable
data class ClaudeErrorResponse(
    val type: String,
    val error: ClaudeError
)

/**
 * Claude Error
 */
@Serializable
data class ClaudeError(
    val type: String,
    val message: String
)


