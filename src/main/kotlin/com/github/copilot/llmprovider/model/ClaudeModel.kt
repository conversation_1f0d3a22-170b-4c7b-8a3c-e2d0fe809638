package com.github.copilot.llmprovider.model

import kotlinx.serialization.*
import kotlinx.serialization.descriptors.*
import kotlinx.serialization.encoding.*
import kotlinx.serialization.json.*

/**
 * Claude Message Request
 */
@Serializable
data class ClaudeMessageRequest(
    val model: String,
    @SerialName("max_tokens")
    val maxTokens: Int,
    val messages: List<ClaudeMessage>,
    val system: String? = null,
    val stream: Boolean = false,
    val temperature: Double? = null,
    @SerialName("top_p")
    val topP: Double? = null,
    @SerialName("top_k")
    val topK: Int? = null,
    val tools: List<ClaudeTool>? = null,
    @SerialName("tool_choice")
    val toolChoice: JsonElement? = null,
    @SerialName("stop_sequences")
    val stopSequences: List<String>? = null
)

/**
 * Claude Message
 */
@Serializable
data class ClaudeMessage(
    val role: String,
    val content: ClaudeMessageContent
) {
    constructor(role: String, content: String) : this(role, ClaudeMessageContent.Text(content))
    constructor(role: String, content: List<ClaudeContentBlock>) : this(role, ClaudeMessageContent.Blocks(content))
}

/**
 * Claude Message Content (can be string or list of content blocks)
 */
@Serializable(with = ClaudeMessageContentSerializer::class)
sealed class ClaudeMessageContent {
    data class Text(val value: String) : ClaudeMessageContent()
    data class Blocks(val value: List<ClaudeContentBlock>) : ClaudeMessageContent()
}

/**
 * Claude Content Block
 */
@Serializable
sealed class ClaudeContentBlock {
    @Serializable
    @SerialName("text")
    data class Text(val text: String, val type: String = "text") : ClaudeContentBlock()
    
    @Serializable
    @SerialName("tool_use")
    data class ToolUse(
        val id: String,
        val name: String,
        val input: Map<String, JsonElement>,
        val type: String = "tool_use"
    ) : ClaudeContentBlock()
    
    @Serializable
    @SerialName("tool_result")
    data class ToolResult(
        @SerialName("tool_use_id")
        val toolUseId: String,
        val content: String,
        @SerialName("is_error")
        val isError: Boolean = false,
        val type: String = "tool_result"
    ) : ClaudeContentBlock()
}

/**
 * Claude Tool
 */
@Serializable
data class ClaudeTool(
    val name: String,
    val description: String,
    @SerialName("input_schema")
    val inputSchema: JsonElement
)

/**
 * Claude Message Response
 */
@Serializable
data class ClaudeMessageResponse(
    val id: String,
    val type: String,
    val role: String,
    val content: List<ClaudeContentBlock>,
    val model: String,
    @SerialName("stop_reason")
    val stopReason: String? = null,
    @SerialName("stop_sequence")
    val stopSequence: String? = null,
    val usage: ClaudeUsage
)

/**
 * Claude Usage
 */
@Serializable
data class ClaudeUsage(
    @SerialName("input_tokens")
    val inputTokens: Int,
    @SerialName("output_tokens")
    val outputTokens: Int
)

/**
 * Claude Error Response
 */
@Serializable
data class ClaudeErrorResponse(
    val type: String,
    val error: ClaudeError
)

/**
 * Claude Error
 */
@Serializable
data class ClaudeError(
    val type: String,
    val message: String
)

/**
 * Custom serializer for ClaudeMessageContent
 */
object ClaudeMessageContentSerializer : KSerializer<ClaudeMessageContent> {
    override val descriptor: SerialDescriptor = buildClassSerialDescriptor("ClaudeMessageContent")

    override fun serialize(encoder: Encoder, value: ClaudeMessageContent) {
        when (value) {
            is ClaudeMessageContent.Text -> encoder.encodeString(value.value)
            is ClaudeMessageContent.Blocks -> encoder.encodeSerializableValue(
                ListSerializer(ClaudeContentBlock.serializer()),
                value.value
            )
        }
    }

    override fun deserialize(decoder: Decoder): ClaudeMessageContent {
        return when (val element = decoder.decodeSerializableValue(JsonElement.serializer())) {
            is JsonPrimitive -> ClaudeMessageContent.Text(element.content)
            is JsonArray -> ClaudeMessageContent.Blocks(
                Json.decodeFromJsonElement(ListSerializer(ClaudeContentBlock.serializer()), element)
            )
            else -> throw SerializationException("Unexpected JSON element type")
        }
    }
}
