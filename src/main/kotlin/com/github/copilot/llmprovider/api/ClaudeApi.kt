package com.github.copilot.llmprovider.api

import com.github.copilot.llmprovider.cli.CliMonitor
import com.github.copilot.llmprovider.model.*
import com.github.copilot.llmprovider.service.ProxyService
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.serialization.json.Json
import mu.KotlinLogging
import java.time.Instant
import java.util.*

private val logger = KotlinLogging.logger {}

/**
 * Claude API 兼容接口
 */
fun Route.claudeApi(proxyService: ProxyService) {
    val cliMonitor = CliMonitor.getInstance()
    
    post("/messages") {
        val startTime = System.currentTimeMillis()
        
        try {
            // 解析请求
            val requestBody = call.receiveText()
            val request = try {
                Json.decodeFromString<ClaudeMessageRequest>(requestBody)
            } catch (e: Exception) {
                logger.warn(e) { "Failed to parse Claude request" }
                call.respond(
                    HttpStatusCode.BadRequest,
                    ClaudeErrorResponse(
                        type = "error",
                        error = ClaudeError(
                            type = "invalid_request_error",
                            message = "Invalid JSON in request body"
                        )
                    )
                )
                return@post
            }

            // 验证请求
            val validationError = validateClaudeRequest(request)
            if (validationError != null) {
                call.respond(HttpStatusCode.BadRequest, validationError)
                return@post
            }

            // 记录请求到 CLI
            cliMonitor.logRequest(
                method = "POST",
                path = "/v1/messages",
                status = 200,
                duration = 0,
                requestBody = requestBody.take(200)
            )

            if (request.stream) {
                // 流式响应
                handleClaudeStreamingResponse(call, request, proxyService, startTime)
            } else {
                // 普通 JSON 响应
                handleClaudeJsonResponse(call, request, proxyService, startTime)
            }

        } catch (e: Exception) {
            logger.error(e) { "Error processing Claude message request" }
            val duration = System.currentTimeMillis() - startTime
            
            cliMonitor.logRequest(
                method = "POST",
                path = "/v1/messages",
                status = 500,
                duration = duration,
                responseBody = "Internal server error"
            )
            
            call.respond(
                HttpStatusCode.InternalServerError,
                ClaudeErrorResponse(
                    type = "error",
                    error = ClaudeError(
                        type = "internal_error",
                        message = "Internal server error"
                    )
                )
            )
        }
    }
}

/**
 * 验证 Claude 请求
 */
private fun validateClaudeRequest(request: ClaudeMessageRequest): ClaudeErrorResponse? {
    if (request.model.isBlank()) {
        return ClaudeErrorResponse(
            type = "error",
            error = ClaudeError(
                type = "invalid_request_error",
                message = "Model is required"
            )
        )
    }
    
    if (request.maxTokens <= 0) {
        return ClaudeErrorResponse(
            type = "error",
            error = ClaudeError(
                type = "invalid_request_error",
                message = "max_tokens must be greater than 0"
            )
        )
    }
    
    if (request.messages.isEmpty()) {
        return ClaudeErrorResponse(
            type = "error",
            error = ClaudeError(
                type = "invalid_request_error",
                message = "At least one message is required"
            )
        )
    }
    
    return null
}

/**
 * 处理 Claude 流式响应
 */
private suspend fun handleClaudeStreamingResponse(
    call: ApplicationCall,
    request: ClaudeMessageRequest,
    proxyService: ProxyService,
    startTime: Long
) {
    call.response.header(HttpHeaders.ContentType, "text/event-stream")
    call.response.header(HttpHeaders.CacheControl, "no-cache")
    call.response.header(HttpHeaders.Connection, "keep-alive")
    
    try {
        // 创建流式响应
        val responseFlow = createClaudeStreamingResponse(request, proxyService)
        
        responseFlow.collect { chunk ->
            call.respondText("data: $chunk\n\n")
        }
        
        // 发送结束标记
        call.respondText("data: [DONE]\n\n")
        
        val duration = System.currentTimeMillis() - startTime
        CliMonitor.getInstance().logRequest(
            method = "POST",
            path = "/v1/messages",
            status = 200,
            duration = duration,
            responseBody = "Streaming response completed"
        )
        
    } catch (e: Exception) {
        logger.error(e) { "Error in Claude streaming response" }
        call.respondText("data: {\"error\": \"Stream error\"}\n\n")
    }
}

/**
 * 处理 Claude JSON 响应
 */
private suspend fun handleClaudeJsonResponse(
    call: ApplicationCall,
    request: ClaudeMessageRequest,
    proxyService: ProxyService,
    startTime: Long
) {
    try {
        // 创建模拟响应（后续会替换为实际的代理调用）
        val response = createMockClaudeResponse(request)
        
        call.respond(HttpStatusCode.OK, response)
        
        val duration = System.currentTimeMillis() - startTime
        val responseBody = Json.encodeToString(ClaudeMessageResponse.serializer(), response)
        
        CliMonitor.getInstance().logRequest(
            method = "POST",
            path = "/v1/messages",
            status = 200,
            duration = duration,
            responseBody = responseBody.take(200)
        )
        
    } catch (e: Exception) {
        logger.error(e) { "Error creating Claude JSON response" }
        throw e
    }
}

/**
 * 创建 Claude 流式响应流
 */
private fun createClaudeStreamingResponse(
    request: ClaudeMessageRequest,
    proxyService: ProxyService
): Flow<String> = flow {
    // 模拟流式响应
    val chunks = listOf("Hello", " there", "! How", " can", " I", " assist", " you", " today", "?")
    
    chunks.forEachIndexed { index, chunk ->
        val response = ClaudeMessageResponse(
            id = "msg_${UUID.randomUUID()}",
            type = "message",
            role = "assistant",
            content = listOf(
                ClaudeContentBlock.Text(text = chunk)
            ),
            model = request.model,
            stopReason = if (index == chunks.size - 1) "end_turn" else null,
            stopSequence = null,
            usage = ClaudeUsage(
                inputTokens = 10,
                outputTokens = index + 1
            )
        )
        
        emit(Json.encodeToString(ClaudeMessageResponse.serializer(), response))
        kotlinx.coroutines.delay(100) // 模拟网络延迟
    }
}

/**
 * 创建模拟 Claude 响应
 */
private fun createMockClaudeResponse(request: ClaudeMessageRequest): ClaudeMessageResponse {
    return ClaudeMessageResponse(
        id = "msg_${UUID.randomUUID()}",
        type = "message",
        role = "assistant",
        content = listOf(
            ClaudeContentBlock.Text(text = "Hello! I'm a mock Claude response. This will be replaced with actual proxy functionality.")
        ),
        model = request.model,
        stopReason = "end_turn",
        stopSequence = null,
        usage = ClaudeUsage(
            inputTokens = 15,
            outputTokens = 20
        )
    )
}
