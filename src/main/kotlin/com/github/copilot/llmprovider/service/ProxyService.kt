package com.github.copilot.llmprovider.service

import com.github.copilot.llmprovider.model.*
import com.github.copilot.llmprovider.util.RequestResponseLogger
import kotlinx.coroutines.flow.Flow
import mu.KotlinLogging

/**
 * 代理服务接口
 * 负责将请求转发到目标 API 服务器
 */
interface ProxyService {
    
    /**
     * 转发 OpenAI 聊天完成请求
     */
    suspend fun forwardOpenAIRequest(request: OpenAIChatCompletionRequest): OpenAIChatCompletionResponse
    
    /**
     * 转发 OpenAI 流式聊天完成请求
     */
    suspend fun forwardOpenAIStreamRequest(request: OpenAIChatCompletionRequest): Flow<String>
    
    /**
     * 转发 Claude 消息请求
     */
    suspend fun forwardClaudeRequest(request: ClaudeMessageRequest): ClaudeMessageResponse
    
    /**
     * 转发 Claude 流式消息请求
     */
    suspend fun forwardClaudeStreamRequest(request: ClaudeMessageRequest): Flow<String>
}

/**
 * 代理服务实现
 */
class ProxyServiceImpl(
    private val authManager: com.github.copilot.llmprovider.auth.AuthManager
) : ProxyService {

    override suspend fun forwardOpenAIRequest(request: OpenAIChatCompletionRequest): OpenAIChatCompletionResponse {
        // 记录请求
        RequestResponseLogger.logOpenAIRequest(request)

        val apiToken = authManager.getValidApiToken()
        val copilotService = authManager.getCopilotService()

        // 转换 OpenAI 请求格式为 GitHub Copilot API 格式
        val messages = request.messages.map { message ->
            mapOf(
                "role" to message.role,
                "content" to (message.content ?: "")
            )
        }

        // 选择合适的模型
        val model = selectBestModel(request.model)

        try {
            val response = copilotService.sendChatCompletion(
                apiToken = apiToken,
                model = model,
                messages = messages,
                stream = false,
                temperature = request.temperature,
                maxTokens = request.maxTokens
            )

            // 记录响应
            RequestResponseLogger.logOpenAIResponse(response)

            return response
        } catch (e: Exception) {
            RequestResponseLogger.logError("OpenAI request failed", e.message)
            throw e
        }
    }

    override suspend fun forwardOpenAIStreamRequest(request: OpenAIChatCompletionRequest): Flow<String> {
        val apiToken = authManager.getValidApiToken()
        val copilotService = authManager.getCopilotService()

        // 转换 OpenAI 请求格式
        val messages = request.messages.map { message ->
            mapOf(
                "role" to message.role,
                "content" to (message.content ?: "")
            )
        }

        // 选择合适的模型
        val model = selectBestModel(request.model)

        return copilotService.sendStreamingChatCompletion(
            apiToken = apiToken,
            model = model,
            messages = messages,
            temperature = request.temperature,
            maxTokens = request.maxTokens
        )
    }

    override suspend fun forwardClaudeRequest(request: ClaudeMessageRequest): ClaudeMessageResponse {
        // 记录 Claude 请求
        RequestResponseLogger.logClaudeRequest(request)

        try {
            // 将 Claude 请求转换为 OpenAI 格式，然后转发
            val openAIRequest = convertClaudeToOpenAI(request)
            val openAIResponse = forwardOpenAIRequest(openAIRequest)

            // 将 OpenAI 响应转换回 Claude 格式
            val claudeResponse = convertOpenAIToClaudeResponse(openAIResponse, request.model)

            // 记录 Claude 响应
            RequestResponseLogger.logClaudeResponse(claudeResponse)

            return claudeResponse
        } catch (e: Exception) {
            RequestResponseLogger.logError("Claude request failed", e.message)
            throw e
        }
    }

    override suspend fun forwardClaudeStreamRequest(request: ClaudeMessageRequest): Flow<String> {
        // 将 Claude 请求转换为 OpenAI 格式，然后转发
        val openAIRequest = convertClaudeToOpenAI(request)
        return forwardOpenAIStreamRequest(openAIRequest)
    }

    /**
     * 选择最佳模型
     */
    private suspend fun selectBestModel(requestedModel: String): String {
        return when {
            requestedModel.contains("claude", ignoreCase = true) -> {
                // 如果请求 Claude 模型，使用首选的 Claude 模型
                authManager.getCopilotService().getPreferredClaudeModel(authManager.getValidApiToken())
            }
            requestedModel.contains("gpt", ignoreCase = true) -> {
                // 如果请求 GPT 模型，尝试映射到可用的模型
                when {
                    requestedModel.contains("gpt-4") -> "gpt-4o"
                    requestedModel.contains("gpt-3.5") -> "gpt-3.5-turbo"
                    else -> requestedModel
                }
            }
            else -> {
                // 默认使用首选的 Claude 模型
                authManager.getCopilotService().getPreferredClaudeModel(authManager.getValidApiToken())
            }
        }
    }

    /**
     * 将 Claude 请求转换为 OpenAI 格式
     */
    private fun convertClaudeToOpenAI(request: ClaudeMessageRequest): OpenAIChatCompletionRequest {
        val messages = mutableListOf<OpenAIMessage>()

        // 添加系统消息（如果有）
        request.system?.let { systemContent ->
            val systemText = ClaudeContentHelper.extractText(systemContent)
            if (systemText.isNotBlank()) {
                messages.add(OpenAIMessage(role = "system", content = systemText))
            }
        }

        // 转换用户和助手消息
        request.messages.forEach { claudeMessage ->
            val content = ClaudeContentHelper.extractText(claudeMessage.content)

            messages.add(OpenAIMessage(
                role = claudeMessage.role,
                content = content
            ))
        }

        return OpenAIChatCompletionRequest(
            model = request.model,
            messages = messages,
            stream = request.stream,
            temperature = request.temperature,
            maxTokens = request.maxTokens
        )
    }

    /**
     * 将 OpenAI 响应转换为 Claude 格式
     */
    private fun convertOpenAIToClaudeResponse(
        response: OpenAIChatCompletionResponse,
        originalModel: String
    ): ClaudeMessageResponse {
        val choice = response.choices.firstOrNull()
        val message = choice?.message

        return ClaudeMessageResponse(
            id = response.id,
            type = "message",
            role = "assistant",
            content = listOf(
                ClaudeContentBlock.Text(text = message?.content ?: "")
            ),
            model = originalModel,
            stopReason = when (choice?.finishReason) {
                "stop" -> "end_turn"
                "length" -> "max_tokens"
                else -> "end_turn"
            },
            stopSequence = null,
            usage = ClaudeUsage(
                inputTokens = response.usage?.promptTokens ?: 0,
                outputTokens = response.usage?.completionTokens ?: 0
            )
        )
    }
}
