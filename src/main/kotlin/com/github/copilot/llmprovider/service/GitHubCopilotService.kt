package com.github.copilot.llmprovider.service

import com.github.copilot.llmprovider.model.OpenAIChatCompletionResponse
import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.engine.*
import io.ktor.client.engine.cio.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import mu.KotlinLogging

private val logger = KotlinLogging.logger {}

/**
 * GitHub Copilot API 服务
 */
class GitHubCopilotService(
    private val httpClientEngine: HttpClientEngine? = null
) {
    @Volatile
    private var cachedApiEndpoint: String = COPILOT_INDIVIDUAL_API_BASE
    companion object {
        const val GITHUB_API_BASE = "https://api.github.com"
        const val COPILOT_API_BASE = "https://api.githubcopilot.com"
        const val COPILOT_INDIVIDUAL_API_BASE = "https://api.individual.githubcopilot.com"
        
        // 首选模型列表（按优先级排序）
        val PREFERRED_CLAUDE_MODELS = listOf(
            "claude-sonnet-4",
            "claude-3.7-sonnet",
            "claude-3.5-sonnet",
            "claude-3-sonnet-20240229",
            "claude-3-haiku"
        )
    }

    private val httpClient = HttpClient(httpClientEngine ?: CIO) {
        install(ContentNegotiation) {
            json(Json {
                ignoreUnknownKeys = true
                isLenient = true
                prettyPrint = false
                encodeDefaults = false
            })
        }
    }

    /**
     * 使用 OAuth token 获取 API token
     */
    suspend fun getApiToken(oauthToken: String): CopilotApiToken {
        logger.info { "Getting Copilot API token..." }

        val response = httpClient.get("$GITHUB_API_BASE/copilot_internal/v2/token") {
            headers {
                append(HttpHeaders.Authorization, "token $oauthToken")
                append(HttpHeaders.Accept, "application/json")
                append(HttpHeaders.UserAgent, "GitHub-Copilot-LLM-Provider/1.0")
            }
        }

        if (response.status != HttpStatusCode.OK) {
            val errorBody = response.bodyAsText()
            logger.error { "Failed to get API token: ${response.status} - $errorBody" }
            throw Exception("Failed to get Copilot API token: ${response.status}")
        }

        val apiToken = response.body<CopilotApiToken>()
        logger.info { "API token obtained, expires at: ${apiToken.expiresAt}" }

        // 缓存 API 端点信息
        cachedApiEndpoint = apiToken.endpoints?.api ?: COPILOT_INDIVIDUAL_API_BASE
        logger.info { "Using API endpoint: $cachedApiEndpoint" }

        return apiToken
    }

    /**
     * 获取支持的模型列表
     */
    suspend fun getSupportedModels(apiToken: String): ModelsResponse {
        logger.info { "Getting supported models..." }
        
        val response = httpClient.get("$cachedApiEndpoint/models") {
            headers {
                append(HttpHeaders.Authorization, "Bearer $apiToken")
                append(HttpHeaders.Accept, "application/json")
                append(HttpHeaders.UserAgent, "GitHub-Copilot-LLM-Provider/1.0")
                append("Editor-Version", "vscode/1.95.0")
                append("Editor-Plugin-Version", "copilot/1.0.0")
            }
        }

        if (response.status != HttpStatusCode.OK) {
            val errorBody = response.bodyAsText()
            logger.error { "Failed to get models: ${response.status} - $errorBody" }
            throw Exception("Failed to get supported models: ${response.status}")
        }

        val models = response.body<ModelsResponse>()
        logger.info { "Found ${models.data.size} supported models" }
        
        // 打印支持的模型
        println("\n🤖 Supported Models:")
        println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
        models.data.forEach { model: ModelInfo ->
            println("📋 ${model.id} (${model.ownedBy ?: "unknown"})")
        }
        println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
        
        return models
    }

    /**
     * 获取首选的 Claude 模型
     */
    suspend fun getPreferredClaudeModel(apiToken: String): String {
        val models = getSupportedModels(apiToken)
        val availableModels = models.data.map { it.id }
        
        // 按优先级查找可用的 Claude 模型
        for (preferredModel in PREFERRED_CLAUDE_MODELS) {
            if (availableModels.contains(preferredModel)) {
                logger.info { "Using preferred Claude model: $preferredModel" }
                return preferredModel
            }
        }
        
        // 如果没有找到首选模型，查找任何 Claude 模型
        val claudeModel = availableModels.find { it.contains("claude", ignoreCase = true) }
        if (claudeModel != null) {
            logger.info { "Using available Claude model: $claudeModel" }
            return claudeModel
        }
        
        // 如果没有 Claude 模型，使用第一个可用模型
        val fallbackModel = availableModels.firstOrNull() ?: "gpt-4"
        logger.warn { "No Claude model found, using fallback: $fallbackModel" }
        return fallbackModel
    }

    /**
     * 发送聊天完成请求
     */
    suspend fun sendChatCompletion(
        apiToken: String,
        model: String,
        messages: List<Map<String, Any>>,
        stream: Boolean = false,
        temperature: Double? = null,
        maxTokens: Int? = null
    ): OpenAIChatCompletionResponse {
        logger.info { "Sending chat completion request with model: $model" }
        
        val requestBodyJson = buildString {
            append("{")
            append("\"model\":\"$model\",")
            append("\"messages\":")
            append(Json.encodeToString(messages))
            append(",\"stream\":$stream")
            temperature?.let { append(",\"temperature\":$it") }
            maxTokens?.let { append(",\"max_tokens\":$it") }
            append("}")
        }

        val response = httpClient.post("$cachedApiEndpoint/chat/completions") {
            headers {
                append(HttpHeaders.Authorization, "Bearer $apiToken")
                append(HttpHeaders.ContentType, "application/json")
                append(HttpHeaders.Accept, "application/json")
                append(HttpHeaders.UserAgent, "GitHub-Copilot-LLM-Provider/1.0")
                append("Editor-Version", "vscode/1.95.0")
                append("Editor-Plugin-Version", "copilot/1.0.0")
            }
            setBody(requestBodyJson)
        }

        if (response.status != HttpStatusCode.OK) {
            val errorBody = response.bodyAsText()
            logger.error { "Chat completion failed: ${response.status} - $errorBody" }
            throw Exception("Chat completion failed: ${response.status}")
        }

        val completionResponse = response.body<OpenAIChatCompletionResponse>()
        logger.info { "Chat completion successful, response ID: ${completionResponse.id}" }
        
        return completionResponse
    }

    /**
     * 发送流式聊天完成请求
     */
    suspend fun sendStreamingChatCompletion(
        apiToken: String,
        model: String,
        messages: List<Map<String, Any>>,
        temperature: Double? = null,
        maxTokens: Int? = null
    ): Flow<String> = flow {
        logger.info { "Sending streaming chat completion request with model: $model" }
        
        val requestBodyJson = buildString {
            append("{")
            append("\"model\":\"$model\",")
            append("\"messages\":")
            append(Json.encodeToString(messages))
            append(",\"stream\":true")
            temperature?.let { append(",\"temperature\":$it") }
            maxTokens?.let { append(",\"max_tokens\":$it") }
            append("}")
        }

        val response = httpClient.post("$cachedApiEndpoint/chat/completions") {
            headers {
                append(HttpHeaders.Authorization, "Bearer $apiToken")
                append(HttpHeaders.ContentType, "application/json")
                append(HttpHeaders.Accept, "text/event-stream")
                append(HttpHeaders.UserAgent, "GitHub-Copilot-LLM-Provider/1.0")
                append("Editor-Version", "vscode/1.95.0")
                append("Editor-Plugin-Version", "copilot/1.0.0")
            }
            setBody(requestBodyJson)
        }

        if (response.status != HttpStatusCode.OK) {
            val errorBody = response.bodyAsText()
            logger.error { "Streaming chat completion failed: ${response.status} - $errorBody" }
            throw Exception("Streaming chat completion failed: ${response.status}")
        }

        // 处理 SSE 流
        val responseText = response.bodyAsText()
        val lines = responseText.split("\n")
        
        for (line in lines) {
            if (line.startsWith("data: ") && line != "data: [DONE]") {
                val data = line.substring(6) // 移除 "data: " 前缀
                emit(data)
            }
        }
    }

    /**
     * 检查 API token 是否过期
     */
    fun isTokenExpired(apiToken: CopilotApiToken): Boolean {
        val currentTime = System.currentTimeMillis() / 1000
        return currentTime >= apiToken.expiresAt
    }

    fun close() {
        httpClient.close()
    }
}

@Serializable
data class CopilotApiToken(
    val token: String,
    @SerialName("expires_at")
    val expiresAt: Long,
    @SerialName("refresh_in")
    val refreshIn: Int,
    @SerialName("annotations_enabled")
    val annotationsEnabled: Boolean? = null,
    @SerialName("chat_enabled")
    val chatEnabled: Boolean? = null,
    val endpoints: CopilotEndpoints? = null,
    val individual: Boolean? = null,
    val sku: String? = null,
    @SerialName("tracking_id")
    val trackingId: String? = null
)

@Serializable
data class CopilotEndpoints(
    val api: String? = null,
    @SerialName("origin-tracker")
    val originTracker: String? = null,
    val proxy: String? = null,
    val telemetry: String? = null
)

@Serializable
data class ModelsResponse(
    val data: List<ModelInfo>,
    val `object`: String? = null
)

@Serializable
data class ModelInfo(
    val id: String,
    val `object`: String? = null,
    val created: Long? = null,
    @SerialName("owned_by")
    val ownedBy: String? = null,
    val capabilities: ModelCapabilities? = null
)

@Serializable
data class ModelCapabilities(
    val family: String? = null,
    val limits: ModelLimits? = null,
    val `object`: String? = null,
    val supports: ModelSupports? = null,
    val tokenizer: String? = null,
    val type: String? = null
)

@Serializable
data class ModelLimits(
    @SerialName("max_context_window_tokens")
    val maxContextWindowTokens: Int? = null,
    @SerialName("max_output_tokens")
    val maxOutputTokens: Int? = null,
    @SerialName("max_prompt_tokens")
    val maxPromptTokens: Int? = null
)

@Serializable
data class ModelSupports(
    val streaming: Boolean? = null,
    @SerialName("tool_calls")
    val toolCalls: Boolean? = null
)
