#!/usr/bin/env python3

import json
import requests
import time

def test_tools_support():
    """测试 tools 支持 - 验证工具调用功能"""
    print("🛠️  Testing GitHub Copilot API Tools Support")
    print("━" * 60)
    
    base_url = "http://localhost:8080"
    
    print("🔧 Tools Support Features:")
    print("  1. ✅ Claude API tools → OpenAI API tools conversion")
    print("  2. ✅ Tool definitions passed to GitHub Copilot API")
    print("  3. ✅ Tool choice parameter support")
    print("  4. ✅ Function calling capabilities")
    print("")
    
    # 测试 1: 简单的工具定义
    print("📝 Test 1: Simple tool definition")
    simple_tools_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 200,
        "messages": [
            {
                "role": "user",
                "content": "What's the weather like in San Francisco? Use the weather tool to check."
            }
        ],
        "tools": [
            {
                "type": "function",
                "function": {
                    "name": "get_weather",
                    "description": "Get the current weather for a location",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "location": {
                                "type": "string",
                                "description": "The city and state, e.g. San Francisco, CA"
                            },
                            "unit": {
                                "type": "string",
                                "enum": ["celsius", "fahrenheit"],
                                "description": "The temperature unit"
                            }
                        },
                        "required": ["location"]
                    }
                }
            }
        ],
        "tool_choice": "auto"
    }
    
    success1 = test_request(base_url, simple_tools_request, "Simple Tools")
    
    # 测试 2: 多个工具定义
    print("\n📝 Test 2: Multiple tools definition")
    multiple_tools_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 250,
        "messages": [
            {
                "role": "user",
                "content": "I need to calculate 15 * 23 and then get the weather for the result as a zip code."
            }
        ],
        "tools": [
            {
                "type": "function",
                "function": {
                    "name": "calculate",
                    "description": "Perform mathematical calculations",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "expression": {
                                "type": "string",
                                "description": "Mathematical expression to evaluate"
                            }
                        },
                        "required": ["expression"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "get_weather",
                    "description": "Get weather information",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "location": {
                                "type": "string",
                                "description": "Location (city, state, or zip code)"
                            }
                        },
                        "required": ["location"]
                    }
                }
            }
        ]
    }
    
    success2 = test_request(base_url, multiple_tools_request, "Multiple Tools")
    
    # 测试 3: 特定工具选择
    print("\n📝 Test 3: Specific tool choice")
    specific_tool_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 150,
        "messages": [
            {
                "role": "user",
                "content": "Calculate the square root of 144."
            }
        ],
        "tools": [
            {
                "type": "function",
                "function": {
                    "name": "calculate",
                    "description": "Perform mathematical calculations",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "expression": {
                                "type": "string",
                                "description": "Mathematical expression"
                            }
                        },
                        "required": ["expression"]
                    }
                }
            }
        ],
        "tool_choice": {
            "type": "function",
            "function": {"name": "calculate"}
        }
    }
    
    success3 = test_request(base_url, specific_tool_request, "Specific Tool Choice")
    
    # 测试 4: 无工具的请求（确保向后兼容）
    print("\n📝 Test 4: Request without tools (backward compatibility)")
    no_tools_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 100,
        "messages": [
            {
                "role": "user",
                "content": "Hello! How are you today?"
            }
        ]
    }
    
    success4 = test_request(base_url, no_tools_request, "No Tools")
    
    print("\n━" * 60)
    print("📊 Tools Support Test Results:")
    print(f"  ✅ Simple tools: {'PASS' if success1 else 'FAIL'}")
    print(f"  ✅ Multiple tools: {'PASS' if success2 else 'FAIL'}")
    print(f"  ✅ Specific tool choice: {'PASS' if success3 else 'FAIL'}")
    print(f"  ✅ Backward compatibility: {'PASS' if success4 else 'FAIL'}")
    
    all_success = success1 and success2 and success3 and success4
    
    if all_success:
        print("\n🎉 All tools tests passed!")
        print("✅ GitHub Copilot API tools support is working correctly!")
        print("✅ Function calling capabilities are enabled!")
    else:
        print("\n❌ Some tools tests failed.")
        print("Check server logs for tools-related errors.")
    
    return all_success

def test_request(base_url, request_data, test_name):
    """测试单个请求，返回是否成功"""
    print(f"🚀 Testing {test_name}...")
    
    # 显示工具信息
    if 'tools' in request_data:
        tools_count = len(request_data['tools'])
        print(f"🛠️  Tools defined: {tools_count}")
        for i, tool in enumerate(request_data['tools']):
            func_name = tool['function']['name']
            print(f"   {i+1}. {func_name}")
    else:
        print("🛠️  No tools defined")
    
    if 'tool_choice' in request_data:
        print(f"🎯 Tool choice: {request_data['tool_choice']}")
    
    try:
        response = requests.post(
            f"{base_url}/v1/messages",
            json=request_data,
            timeout=60
        )
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Success!")
            try:
                response_data = response.json()
                content = response_data.get('content', [])
                if content and len(content) > 0:
                    text = content[0].get('text', '')
                    print(f"📏 Response length: {len(text)} characters")
                    print(f"🤖 Response preview: {text[:80]}{'...' if len(text) > 80 else ''}")
                    
                    # 检查是否包含工具调用相关内容
                    if 'tools' in request_data:
                        if any(keyword in text.lower() for keyword in [
                            'function', 'tool', 'call', 'calculate', 'weather'
                        ]):
                            print("✅ Response mentions tools/functions!")
                        else:
                            print("⚠️  Response doesn't mention tools (may be normal)")
                    
                    return True
                else:
                    print("⚠️  No content in response")
                    return False
            except json.JSONDecodeError:
                print("❌ Invalid JSON response")
                return False
                
        elif response.status_code == 500:
            print("❌ Server error (500)")
            error_text = response.text
            
            # 检查是否是工具相关错误
            if any(keyword in error_text.lower() for keyword in [
                'tool', 'function', 'serialization', 'json'
            ]):
                print("🔍 Tools-related error detected!")
                print(f"   Error: {error_text[:200]}")
                return False
            else:
                print("⚠️  Other server error")
                print(f"   Error: {error_text[:150]}")
                return False
                
        else:
            print(f"⚠️  Unexpected status: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - make sure server is running")
        return False
    except requests.exceptions.Timeout:
        print("⏰ Request timeout")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 GitHub Copilot API Tools Support Test")
    print("Make sure the server is running on http://localhost:8080")
    print("━" * 60)
    print("This test verifies that tools are correctly:")
    print("  ✅ Parsed from Claude API requests")
    print("  ✅ Converted to OpenAI API format")
    print("  ✅ Passed to GitHub Copilot API")
    print("  ✅ Function calling capabilities work")
    print("━" * 60)
    
    # 检查服务器状态
    try:
        response = requests.get("http://localhost:8080/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print("⚠️  Server responded but may not be healthy")
    except:
        print("❌ Server is not running. Please start it first:")
        print("   ./run_debug_test.sh")
        exit(1)
    
    print("━" * 60)
    
    # 运行测试
    success = test_tools_support()
    
    print("━" * 60)
    if success:
        print("🎉 TOOLS SUPPORT TEST PASSED!")
        print("GitHub Copilot API tools support is working correctly!")
    else:
        print("❌ Tools support test failed.")
        print("Check server logs for tools-related issues.")
