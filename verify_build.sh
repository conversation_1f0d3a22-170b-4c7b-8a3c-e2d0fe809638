#!/bin/bash

set -e

echo "🔍 Build Verification Script"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# 1. 检查 Java 版本
echo "☕ Checking Java version..."
java -version

# 2. 清理之前的构建
echo "🧹 Cleaning previous builds..."
./gradlew clean

# 3. 编译 Kotlin 代码
echo "🔨 Compiling Kotlin code..."
./gradlew compileKotlin

# 4. 运行测试
echo "🧪 Running tests..."
./gradlew test --continue || echo "⚠️  Some tests may have failed, but continuing..."

# 5. 构建 fat jar
echo "📦 Building fat jar..."
./gradlew shadowJar

# 6. 检查生成的 jar 文件
echo "📋 Checking generated jar..."
if [ -f "build/libs/llm-provider.jar" ]; then
    echo "✅ JAR file created successfully"
    ls -lh build/libs/llm-provider.jar
else
    echo "❌ JAR file not found"
    exit 1
fi

# 7. 检查 jar 文件内容
echo "📂 Checking jar contents..."
jar tf build/libs/llm-provider.jar | head -10
echo "..."
echo "Total entries: $(jar tf build/libs/llm-provider.jar | wc -l)"

# 8. 验证主类
echo "🎯 Verifying main class..."
if jar tf build/libs/llm-provider.jar | grep -q "com/github/copilot/llmprovider/MainKt.class"; then
    echo "✅ Main class found in jar"
else
    echo "❌ Main class not found in jar"
    exit 1
fi

# 9. 检查 Docker 构建（如果 Docker 可用）
if command -v docker &> /dev/null; then
    echo "🐳 Testing Docker build..."
    docker build -t github-copilot-llm-provider-test . > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "✅ Docker build successful"
        docker rmi github-copilot-llm-provider-test > /dev/null 2>&1
    else
        echo "❌ Docker build failed"
    fi
else
    echo "⚠️  Docker not available, skipping Docker build test"
fi

echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "🎉 Build verification completed successfully!"
echo ""
echo "📋 Next steps:"
echo "   1. Run the application: java -jar build/libs/llm-provider.jar"
echo "   2. Test with Docker: ./test_docker.sh"
echo "   3. Test endpoints: python3 test_server.py"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
