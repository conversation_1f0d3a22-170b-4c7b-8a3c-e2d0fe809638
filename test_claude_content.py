#!/usr/bin/env python3

import json
import requests
import time

def test_claude_content_formats():
    """测试 Claude API 的不同 content 格式"""
    print("🧪 Testing Claude Content Format Handling")
    print("━" * 60)
    
    base_url = "http://localhost:8080"
    
    # 测试 1: 简单字符串 content
    print("📝 Test 1: Simple string content")
    simple_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 100,
        "messages": [
            {
                "role": "user",
                "content": "Hello, how are you?"
            }
        ],
        "stream": False
    }
    
    test_request(base_url, simple_request, "Simple String Content")
    
    # 测试 2: 复杂内容块数组 content
    print("\n📝 Test 2: Complex content blocks")
    complex_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 100,
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "Hello, I have a question about programming."
                    }
                ]
            }
        ],
        "stream": False
    }
    
    test_request(base_url, complex_request, "Complex Content Blocks")
    
    # 测试 3: 带系统消息的请求
    print("\n📝 Test 3: Request with system message")
    system_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 100,
        "system": "You are a helpful programming assistant.",
        "messages": [
            {
                "role": "user",
                "content": "What is Python?"
            }
        ],
        "stream": False
    }
    
    test_request(base_url, system_request, "System Message Request")
    
    # 测试 4: 多轮对话
    print("\n📝 Test 4: Multi-turn conversation")
    multi_turn_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 100,
        "messages": [
            {
                "role": "user",
                "content": "What is 2+2?"
            },
            {
                "role": "assistant",
                "content": "2+2 equals 4."
            },
            {
                "role": "user",
                "content": "What about 3+3?"
            }
        ],
        "stream": False
    }
    
    test_request(base_url, multi_turn_request, "Multi-turn Conversation")
    
    # 测试 5: 流式响应
    print("\n📝 Test 5: Streaming response")
    streaming_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 100,
        "messages": [
            {
                "role": "user",
                "content": "Tell me a short story."
            }
        ],
        "stream": True
    }
    
    test_streaming_request(base_url, streaming_request, "Streaming Response")
    
    print("\n━" * 60)
    print("✨ All Claude content format tests completed!")

def test_request(base_url, request_data, test_name):
    """测试单个请求"""
    print(f"🚀 Testing: {test_name}")
    
    try:
        response = requests.post(
            f"{base_url}/v1/messages",
            json=request_data,
            timeout=30
        )
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Request successful!")
            try:
                response_data = response.json()
                print(f"🆔 Response ID: {response_data.get('id', 'N/A')}")
                print(f"📋 Model: {response_data.get('model', 'N/A')}")
                
                content = response_data.get('content', [])
                if content:
                    for i, block in enumerate(content):
                        if isinstance(block, dict) and block.get('type') == 'text':
                            text = block.get('text', '')
                            print(f"🤖 Response {i+1}: {text[:100]}{'...' if len(text) > 100 else ''}")
                
            except json.JSONDecodeError:
                print("⚠️  Response is not valid JSON")
                print(f"📄 Raw response: {response.text[:200]}")
                
        elif response.status_code == 500:
            print("❌ Server error (500)")
            print(f"📄 Error details: {response.text[:300]}")
        else:
            print(f"⚠️  Unexpected status: {response.status_code}")
            print(f"📄 Response: {response.text[:200]}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def test_streaming_request(base_url, request_data, test_name):
    """测试流式请求"""
    print(f"🌊 Testing: {test_name}")
    
    try:
        response = requests.post(
            f"{base_url}/v1/messages",
            json=request_data,
            stream=True,
            timeout=30
        )
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Streaming started!")
            print("🔄 Streaming content:")
            
            chunk_count = 0
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data: ') and line_str != 'data: [DONE]':
                        chunk_count += 1
                        if chunk_count <= 5:  # 只显示前5个块
                            data = line_str[6:]  # 移除 'data: ' 前缀
                            print(f"   📦 Chunk {chunk_count}: {data[:50]}{'...' if len(data) > 50 else ''}")
            
            print(f"📈 Total chunks received: {chunk_count}")
            
        else:
            print(f"❌ Streaming failed: {response.status_code}")
            print(f"📄 Error: {response.text[:200]}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Streaming request failed: {e}")
    except Exception as e:
        print(f"❌ Unexpected streaming error: {e}")

if __name__ == "__main__":
    print("🚀 Starting Claude Content Format Tests")
    print("Make sure the server is running on http://localhost:8080")
    print("━" * 60)
    
    # 等待一下确保服务器启动
    time.sleep(2)
    
    # 运行测试
    test_claude_content_formats()
    
    print("━" * 60)
