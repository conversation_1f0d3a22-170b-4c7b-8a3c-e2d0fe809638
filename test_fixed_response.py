#!/usr/bin/env python3

import json
import requests
import time

def test_fixed_response():
    """测试修复后的 GitHub Copilot API 响应解析"""
    print("✅ Testing Fixed GitHub Copilot API Response Parsing")
    print("━" * 60)
    
    base_url = "http://localhost:8080"
    
    tests = [
        # 测试 1: 简单对话
        {
            "name": "Simple Conversation",
            "request": {
                "model": "claude-3.5-sonnet",
                "max_tokens": 100,
                "messages": [
                    {
                        "role": "user",
                        "content": "Hello! Please introduce yourself briefly."
                    }
                ],
                "temperature": 0.7
            }
        },
        
        # 测试 2: 代码生成
        {
            "name": "Code Generation",
            "request": {
                "model": "claude-3.5-sonnet",
                "max_tokens": 200,
                "messages": [
                    {
                        "role": "user",
                        "content": "Write a Python function to reverse a string."
                    }
                ],
                "temperature": 0.3
            }
        },
        
        # 测试 3: 复杂格式（数组内容）
        {
            "name": "Complex Array Content",
            "request": {
                "model": "claude-3.5-sonnet",
                "max_tokens": 150,
                "system": [
                    {
                        "type": "text",
                        "text": "You are a helpful programming assistant."
                    }
                ],
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "Explain what this code does: print('Hello, World!')"
                            }
                        ]
                    }
                ],
                "temperature": 0.5
            }
        }
    ]
    
    success_count = 0
    total_count = len(tests)
    
    for i, test in enumerate(tests, 1):
        print(f"\n📝 Test {i}/{total_count}: {test['name']}")
        print("─" * 40)
        
        success = test_request(base_url, test['request'], test['name'])
        if success:
            success_count += 1
        
        time.sleep(1)  # 避免请求过快
    
    print(f"\n━" * 60)
    print(f"📊 Test Results: {success_count}/{total_count} tests passed")
    
    if success_count == total_count:
        print("🎉 All tests passed! GitHub Copilot API integration is working!")
        print("✅ JSON parsing issues have been resolved!")
        return True
    else:
        print(f"⚠️  {total_count - success_count} tests failed.")
        return False

def test_request(base_url, request_data, test_name):
    """测试单个请求，返回是否成功"""
    try:
        response = requests.post(
            f"{base_url}/v1/messages",
            json=request_data,
            timeout=60
        )
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Success!")
            try:
                response_data = response.json()
                
                # 验证响应结构
                if 'content' in response_data and response_data['content']:
                    content = response_data['content']
                    if isinstance(content, list) and len(content) > 0:
                        first_block = content[0]
                        if isinstance(first_block, dict) and first_block.get('type') == 'text':
                            text = first_block.get('text', '')
                            print(f"🤖 Response: {text[:80]}{'...' if len(text) > 80 else ''}")
                            
                            # 验证这是真实的 AI 响应
                            if text and len(text.strip()) > 0:
                                print("✅ Received valid AI response!")
                                
                                # 检查响应质量
                                if len(text.strip()) > 10:
                                    print("✅ Response has good length!")
                                    return True
                                else:
                                    print("⚠️  Response is very short")
                                    return True  # 仍然算成功，只是内容短
                            else:
                                print("⚠️  Empty response content")
                                return False
                        else:
                            print("⚠️  Invalid content block structure")
                            print(f"   Block: {first_block}")
                            return False
                    else:
                        print("⚠️  Empty or invalid content array")
                        return False
                else:
                    print("⚠️  No content in response")
                    print(f"   Response keys: {list(response_data.keys())}")
                    return False
                    
            except json.JSONDecodeError as e:
                print("❌ Response is not valid JSON")
                print(f"   JSON Error: {e}")
                print(f"📄 Raw response: {response.text[:200]}")
                return False
                
        elif response.status_code == 500:
            print("❌ Server error (500)")
            error_text = response.text
            print(f"📄 Error: {error_text[:300]}")
            
            # 检查是否还有 JSON 解析错误
            if any(keyword in error_text for keyword in [
                "JsonDecodingException",
                "JsonConvertException", 
                "unknown key",
                "required for type"
            ]):
                print("🔍 JSON parsing error still detected!")
                print("   This indicates the model fix may not be complete.")
                return False
            else:
                print("⚠️  Other server error (not JSON parsing related)")
                return False
                
        elif response.status_code in [401, 403]:
            print(f"❌ Authentication error ({response.status_code})")
            print("💡 Check GitHub Copilot subscription and token")
            return False
            
        else:
            print(f"⚠️  Unexpected status: {response.status_code}")
            print(f"📄 Response: {response.text[:200]}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ Request timeout")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - make sure server is running")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Fixed GitHub Copilot API Response Tests")
    print("Make sure the server is running on http://localhost:8080")
    print("━" * 60)
    print("This test verifies that the JSON parsing issues have been resolved:")
    print("  ✅ OpenAIChoice.index field is now optional")
    print("  ✅ OpenAIUsage.prompt_tokens_details field is supported")
    print("  ✅ All GitHub Copilot API response fields are handled")
    print("━" * 60)
    
    # 检查服务器是否运行
    try:
        response = requests.get("http://localhost:8080/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print("⚠️  Server responded but may not be healthy")
    except:
        print("❌ Server is not running. Please start it first:")
        print("   ./run_debug_test.sh")
        exit(1)
    
    print("━" * 60)
    
    # 运行测试
    success = test_fixed_response()
    
    print("━" * 60)
    if success:
        print("🎉 All tests completed successfully!")
        print("GitHub Copilot Claude LLM Provider is working correctly!")
        print("JSON parsing issues have been resolved!")
    else:
        print("❌ Some tests failed. Check the output above.")
        print("If you see JSON parsing errors, the model may need further adjustments.")
