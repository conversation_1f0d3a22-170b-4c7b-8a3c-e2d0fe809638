#!/usr/bin/env python3

import json
import requests
import time

def test_server_endpoints():
    """测试服务器端点是否正常工作"""
    print("🧪 Testing Server Endpoints")
    print("━" * 50)
    
    base_url = "http://localhost:8080"
    
    # 1. 测试健康检查
    print("🏥 Testing health check...")
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Health check passed")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False
    
    # 2. 测试根端点
    print("🏠 Testing root endpoint...")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Root endpoint: {data.get('name', 'Unknown')}")
        else:
            print(f"❌ Root endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Root endpoint error: {e}")
    
    # 3. 测试 OpenAI API 端点（应该返回错误，但不是 500）
    print("🤖 Testing OpenAI API endpoint...")
    try:
        openai_request = {
            "model": "gpt-4",
            "messages": [
                {
                    "role": "user",
                    "content": "Hello, test!"
                }
            ],
            "stream": False
        }
        
        response = requests.post(
            f"{base_url}/v1/chat/completions",
            json=openai_request,
            timeout=10
        )
        
        print(f"📊 OpenAI API response: {response.status_code}")
        if response.status_code in [200, 401, 403]:
            print("✅ OpenAI API endpoint is working (auth may be needed)")
        elif response.status_code == 500:
            print("❌ OpenAI API endpoint has server error")
            print(f"Response: {response.text[:200]}")
        else:
            print(f"⚠️  OpenAI API endpoint returned: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ OpenAI API error: {e}")
    
    # 4. 测试 Claude API 端点
    print("🧠 Testing Claude API endpoint...")
    try:
        claude_request = {
            "model": "claude-3.5-sonnet",
            "max_tokens": 100,
            "messages": [
                {
                    "role": "user",
                    "content": "Hello, test!"
                }
            ],
            "stream": False
        }
        
        response = requests.post(
            f"{base_url}/v1/messages",
            json=claude_request,
            timeout=10
        )
        
        print(f"📊 Claude API response: {response.status_code}")
        if response.status_code in [200, 401, 403]:
            print("✅ Claude API endpoint is working (auth may be needed)")
        elif response.status_code == 500:
            print("❌ Claude API endpoint has server error")
            print(f"Response: {response.text[:200]}")
        else:
            print(f"⚠️  Claude API endpoint returned: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ Claude API error: {e}")
    
    # 5. 测试无效 JSON
    print("🚫 Testing invalid JSON handling...")
    try:
        response = requests.post(
            f"{base_url}/v1/chat/completions",
            data="invalid json",
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        
        if response.status_code == 400:
            print("✅ Invalid JSON handled correctly")
        else:
            print(f"⚠️  Invalid JSON returned: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Invalid JSON test error: {e}")
    
    print("\n━" * 50)
    print("✨ Server endpoint tests completed!")
    return True

def test_json_parsing():
    """测试 JSON 解析修复"""
    print("\n🔧 Testing JSON Parsing Fixes")
    print("━" * 50)
    
    # 测试包含 metadata 字段的请求
    test_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 100,
        "messages": [
            {
                "role": "user",
                "content": "Hello!"
            }
        ],
        "temperature": 0.7,
        "metadata": {
            "user_id": "test_user",
            "session_id": "test_session"
        },
        "stream": False
    }
    
    print("📝 Testing request with metadata field...")
    try:
        response = requests.post(
            "http://localhost:8080/v1/messages",
            json=test_request,
            timeout=10
        )
        
        if response.status_code != 500:
            print("✅ Metadata field handled correctly (no 500 error)")
            print(f"📊 Response status: {response.status_code}")
        else:
            print("❌ Still getting 500 error with metadata field")
            print(f"Response: {response.text[:300]}")
            
    except Exception as e:
        print(f"❌ Metadata test error: {e}")
    
    print("━" * 50)

if __name__ == "__main__":
    print("🚀 Starting Server Tests")
    print("Make sure the server is running on http://localhost:8080")
    print("━" * 60)
    
    # 等待一下确保服务器启动
    time.sleep(2)
    
    # 运行测试
    server_ok = test_server_endpoints()
    test_json_parsing()
    
    if server_ok:
        print("\n🎉 All basic server tests passed!")
    else:
        print("\n❌ Some server tests failed!")
    
    print("━" * 60)
