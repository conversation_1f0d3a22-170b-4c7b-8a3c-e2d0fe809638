# GitHub Copilot LLM Provider

一个代理服务，提供 OpenAI API 和 Claude API 兼容的接口，支持将请求转发到其他 LLM 服务提供商。

## 功能特性

### ✅ 已实现功能

- **OpenAI API 兼容**
  - `/v1/chat/completions` 端点
  - 支持流式 (SSE) 和非流式响应
  - 完整的请求验证和错误处理
  - <PERSON><PERSON> calls 支持

- **Claude API 兼容**
  - `/v1/messages` 端点
  - 支持流式 (SSE) 和非流式响应
  - Claude 特有的消息格式支持
  - System message 支持

- **实时监控**
  - CLI 监控界面，实时显示请求和响应
  - 请求统计和性能监控
  - 彩色终端输出

- **健壮性**
  - 完整的错误处理和验证
  - CORS 支持
  - 结构化日志记录

### 🚧 开发中功能

- 实际的代理转发逻辑（当前为模拟响应）
- CLI 监控界面的完善
- Docker 容器化

## 快速开始

### 构建和运行

```bash
# 构建项目
./gradlew shadowJar

# 运行应用
java -jar build/libs/llm-provider.jar
```

应用将在 `http://localhost:8080` 启动。

### 环境变量

- `PORT`: 服务器端口 (默认: 8080)
- `HOST`: 服务器主机 (默认: 0.0.0.0)
- `TARGET_API_URL`: 目标 API 服务器 URL (默认: http://localhost:11434)

### API 使用示例

#### OpenAI API 兼容

```bash
# 非流式请求
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-4",
    "messages": [
      {
        "role": "user",
        "content": "Hello, world!"
      }
    ],
    "stream": false
  }'

# 流式请求
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-4",
    "messages": [
      {
        "role": "user",
        "content": "Hello, world!"
      }
    ],
    "stream": true
  }'
```

#### Claude API 兼容

```bash
# 非流式请求
curl -X POST http://localhost:8080/v1/messages \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-sonnet-20240229",
    "max_tokens": 1000,
    "messages": [
      {
        "role": "user",
        "content": "Hello, Claude!"
      }
    ],
    "stream": false
  }'
```

#### 健康检查

```bash
curl http://localhost:8080/health
```

## 项目结构

```
src/
├── main/kotlin/com/github/copilot/llmprovider/
│   ├── Main.kt                 # 应用程序入口
│   ├── api/                    # API 接口实现
│   │   ├── OpenAIApi.kt       # OpenAI API 兼容接口
│   │   └── ClaudeApi.kt       # Claude API 兼容接口
│   ├── cli/                    # CLI 监控界面
│   │   └── CliMonitor.kt      # 实时监控显示
│   ├── model/                  # 数据模型
│   │   ├── OpenAIModel.kt     # OpenAI API 数据模型
│   │   └── ClaudeModel.kt     # Claude API 数据模型
│   ├── server/                 # 服务器配置
│   │   └── Server.kt          # Ktor 服务器配置
│   └── service/                # 业务服务
│       └── ProxyService.kt    # 代理转发服务
└── test/                       # 测试代码
    └── kotlin/com/github/copilot/llmprovider/
        ├── api/               # API 测试
        ├── model/             # 模型测试
        └── server/            # 服务器测试
```

## 技术栈

- **Kotlin**: 主要编程语言
- **Ktor**: HTTP 服务器框架
- **Kotlinx Serialization**: JSON 序列化
- **Mordant**: 终端 UI 库
- **JUnit 5**: 测试框架
- **Gradle**: 构建工具

## 开发

### 运行测试

```bash
# 运行所有测试
./gradlew test

# 运行特定测试
./gradlew test --tests "*OpenAIApiTest*"
./gradlew test --tests "*ClaudeApiTest*"
```

### 开发模式

```bash
# 使用 Gradle 运行（支持热重载）
./gradlew run
```

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！

## 下一步计划

1. 实现实际的代理转发逻辑
2. 完善 CLI 监控界面
3. 添加 Docker 支持
4. 添加配置文件支持
5. 添加更多的 API 兼容性
6. 性能优化和缓存
7. 添加认证和授权
8. 添加速率限制
9. 添加监控和指标收集
10. 添加负载均衡支持
