#!/usr/bin/env python3

import json
import requests
import time

def test_claude_tools_format():
    """测试 Claude API tools 格式转换"""
    print("🔄 Testing Claude API Tools Format Conversion")
    print("━" * 60)
    
    base_url = "http://localhost:8080"
    
    print("🔧 Claude → OpenAI Tools Format Conversion:")
    print("  Claude: {name, description, input_schema}")
    print("  OpenAI: {type: 'function', function: {name, description, parameters}}")
    print("")
    
    # 测试 1: Claude 格式的单个工具
    print("📝 Test 1: Claude format single tool")
    claude_single_tool_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 150,
        "messages": [
            {
                "role": "user",
                "content": "Create a task for me to write a Python script."
            }
        ],
        "tools": [
            {
                "name": "Task",
                "description": "Create a new task with description and prompt",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "description": {
                            "type": "string",
                            "description": "A short (3-5 word) description of the task"
                        },
                        "prompt": {
                            "type": "string",
                            "description": "The task for the agent to perform"
                        }
                    },
                    "required": [
                        "description",
                        "prompt"
                    ],
                    "additionalProperties": False,
                    "$schema": "http://json-schema.org/draft-07/schema#"
                }
            }
        ]
    }
    
    success1 = test_request(base_url, claude_single_tool_request, "Claude Single Tool")
    
    # 测试 2: Claude 格式的多个工具
    print("\n📝 Test 2: Claude format multiple tools")
    claude_multiple_tools_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 200,
        "messages": [
            {
                "role": "user",
                "content": "Help me create a task and calculate something."
            }
        ],
        "tools": [
            {
                "name": "Task",
                "description": "Create a new task",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "description": {
                            "type": "string",
                            "description": "Task description"
                        },
                        "prompt": {
                            "type": "string",
                            "description": "Task prompt"
                        }
                    },
                    "required": ["description", "prompt"]
                }
            },
            {
                "name": "Calculator",
                "description": "Perform mathematical calculations",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "expression": {
                            "type": "string",
                            "description": "Mathematical expression to evaluate"
                        },
                        "precision": {
                            "type": "integer",
                            "description": "Number of decimal places",
                            "default": 2
                        }
                    },
                    "required": ["expression"],
                    "additionalProperties": False
                }
            }
        ]
    }
    
    success2 = test_request(base_url, claude_multiple_tools_request, "Claude Multiple Tools")
    
    # 测试 3: 混合格式（Claude + OpenAI）
    print("\n📝 Test 3: Mixed format (Claude + OpenAI)")
    mixed_format_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 180,
        "messages": [
            {
                "role": "user",
                "content": "Use both task creation and weather tools."
            }
        ],
        "tools": [
            {
                # Claude 格式
                "name": "Task",
                "description": "Create a task",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "description": {"type": "string"},
                        "prompt": {"type": "string"}
                    },
                    "required": ["description", "prompt"]
                }
            },
            {
                # OpenAI 格式
                "type": "function",
                "function": {
                    "name": "get_weather",
                    "description": "Get weather information",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "location": {
                                "type": "string",
                                "description": "City name"
                            }
                        },
                        "required": ["location"]
                    }
                }
            }
        ]
    }
    
    success3 = test_request(base_url, mixed_format_request, "Mixed Format")
    
    # 测试 4: 复杂的 Claude 格式
    print("\n📝 Test 4: Complex Claude format with nested properties")
    complex_claude_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 160,
        "messages": [
            {
                "role": "user",
                "content": "Create a complex configuration task."
            }
        ],
        "tools": [
            {
                "name": "ConfigTask",
                "description": "Create a configuration task with complex parameters",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "name": {
                            "type": "string",
                            "description": "Configuration name"
                        },
                        "settings": {
                            "type": "object",
                            "properties": {
                                "enabled": {"type": "boolean"},
                                "level": {
                                    "type": "string",
                                    "enum": ["low", "medium", "high"]
                                },
                                "options": {
                                    "type": "array",
                                    "items": {"type": "string"}
                                }
                            },
                            "required": ["enabled"]
                        }
                    },
                    "required": ["name", "settings"],
                    "additionalProperties": False,
                    "$schema": "http://json-schema.org/draft-07/schema#"
                }
            }
        ]
    }
    
    success4 = test_request(base_url, complex_claude_request, "Complex Claude Format")
    
    print("\n━" * 60)
    print("📊 Claude Tools Format Test Results:")
    print(f"  ✅ Claude single tool: {'PASS' if success1 else 'FAIL'}")
    print(f"  ✅ Claude multiple tools: {'PASS' if success2 else 'FAIL'}")
    print(f"  ✅ Mixed format: {'PASS' if success3 else 'FAIL'}")
    print(f"  ✅ Complex Claude format: {'PASS' if success4 else 'FAIL'}")
    
    all_success = success1 and success2 and success3 and success4
    
    if all_success:
        print("\n🎉 All Claude format tests passed!")
        print("✅ Claude API tools format conversion is working correctly!")
        print("✅ Both Claude and OpenAI formats are supported!")
    else:
        print("\n❌ Some Claude format tests failed.")
        print("Check server logs for conversion errors.")
    
    return all_success

def test_request(base_url, request_data, test_name):
    """测试单个请求，返回是否成功"""
    print(f"🚀 Testing {test_name}...")
    
    # 显示工具信息
    if 'tools' in request_data:
        tools_count = len(request_data['tools'])
        print(f"🛠️  Tools defined: {tools_count}")
        for i, tool in enumerate(request_data['tools']):
            if 'name' in tool:
                # Claude 格式
                print(f"   {i+1}. {tool['name']} (Claude format)")
            elif 'function' in tool:
                # OpenAI 格式
                print(f"   {i+1}. {tool['function']['name']} (OpenAI format)")
    
    try:
        response = requests.post(
            f"{base_url}/v1/messages",
            json=request_data,
            timeout=60
        )
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Success!")
            try:
                response_data = response.json()
                content = response_data.get('content', [])
                if content and len(content) > 0:
                    text = content[0].get('text', '')
                    print(f"📏 Response length: {len(text)} characters")
                    print(f"🤖 Response preview: {text[:80]}{'...' if len(text) > 80 else ''}")
                    
                    # 检查是否包含工具相关内容
                    if any(keyword in text.lower() for keyword in [
                        'task', 'create', 'calculate', 'weather', 'config'
                    ]):
                        print("✅ Response mentions tools/functions!")
                    else:
                        print("⚠️  Response doesn't mention tools (may be normal)")
                    
                    return True
                else:
                    print("⚠️  No content in response")
                    return False
            except json.JSONDecodeError:
                print("❌ Invalid JSON response")
                return False
                
        elif response.status_code == 500:
            print("❌ Server error (500)")
            error_text = response.text
            
            # 检查是否是工具转换相关错误
            if any(keyword in error_text.lower() for keyword in [
                'tool', 'function', 'conversion', 'claude', 'openai'
            ]):
                print("🔍 Tools conversion error detected!")
                print(f"   Error: {error_text[:200]}")
                return False
            else:
                print("⚠️  Other server error")
                print(f"   Error: {error_text[:150]}")
                return False
                
        else:
            print(f"⚠️  Unexpected status: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - make sure server is running")
        return False
    except requests.exceptions.Timeout:
        print("⏰ Request timeout")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Claude API Tools Format Conversion Test")
    print("Make sure the server is running on http://localhost:8080")
    print("━" * 60)
    print("This test verifies Claude format conversion:")
    print("  ✅ Claude {name, input_schema} → OpenAI {type, function}")
    print("  ✅ input_schema → parameters conversion")
    print("  ✅ Mixed format support")
    print("  ✅ Complex nested properties")
    print("━" * 60)
    
    # 检查服务器状态
    try:
        response = requests.get("http://localhost:8080/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print("⚠️  Server responded but may not be healthy")
    except:
        print("❌ Server is not running. Please start it first:")
        print("   ./run_debug_test.sh")
        exit(1)
    
    print("━" * 60)
    
    # 运行测试
    success = test_claude_tools_format()
    
    print("━" * 60)
    if success:
        print("🎉 CLAUDE TOOLS FORMAT TEST PASSED!")
        print("Claude API tools format conversion is working correctly!")
    else:
        print("❌ Claude tools format test failed.")
        print("Check server logs for conversion issues.")
