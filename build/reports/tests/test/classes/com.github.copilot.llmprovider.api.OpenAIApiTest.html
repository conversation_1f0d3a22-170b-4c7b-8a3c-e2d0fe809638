<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - OpenAIApiTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>OpenAIApiTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.github.copilot.llmprovider.api.html">com.github.copilot.llmprovider.api</a> &gt; OpenAIApiTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">6</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">2.946s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
<li>
<a href="#">Standard output</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">should handle chat completions request()</td>
<td class="success">0.011s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle invalid JSON in request()</td>
<td class="success">1.980s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle streaming chat completions request()</td>
<td class="success">0.943s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle tool calls in request()</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle unsupported model()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should validate required fields in request()</td>
<td class="success">0.005s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>13:58:03.297 [DefaultDispatcher-worker-1 @call-context#2] INFO  ktor.test - No ktor.deployment.watch patterns specified, automatic reload is not active.
13:58:05.039 [DefaultDispatcher-worker-1 @call-context#2] INFO  ktor.test - Application started in 1.745 seconds.
13:58:05.040 [DefaultDispatcher-worker-3 @coroutine#4] INFO  ktor.test - Responding at http://localhost:80
13:58:05.040 [DefaultDispatcher-worker-3 @coroutine#4] INFO  ktor.test - Responding at https://localhost:443
13:58:05.064 [DefaultDispatcher-worker-1 @request#2] WARN  c.g.c.llmprovider.api.OpenAIApi - Failed to parse OpenAI request
kotlinx.serialization.json.internal.JsonDecodingException: Unexpected JSON token at offset 0: Expected start of the object '{', but had 'i' instead at path: $
JSON input: invalid json
	at kotlinx.serialization.json.internal.JsonExceptionsKt.JsonDecodingException(JsonExceptions.kt:24)
	at kotlinx.serialization.json.internal.JsonExceptionsKt.JsonDecodingException(JsonExceptions.kt:32)
	at kotlinx.serialization.json.internal.AbstractJsonLexer.fail(AbstractJsonLexer.kt:598)
	at kotlinx.serialization.json.internal.AbstractJsonLexer.fail$default(AbstractJsonLexer.kt:596)
	at kotlinx.serialization.json.internal.AbstractJsonLexer.fail$kotlinx_serialization_json(AbstractJsonLexer.kt:233)
	at kotlinx.serialization.json.internal.AbstractJsonLexer.fail$kotlinx_serialization_json$default(AbstractJsonLexer.kt:228)
	at kotlinx.serialization.json.internal.AbstractJsonLexer.unexpectedToken(AbstractJsonLexer.kt:225)
	at kotlinx.serialization.json.internal.StringJsonLexer.consumeNextToken(StringJsonLexer.kt:74)
	at kotlinx.serialization.json.internal.StreamingJsonDecoder.beginStructure(StreamingJsonDecoder.kt:100)
	at com.github.copilot.llmprovider.model.OpenAIChatCompletionRequest$$serializer.deserialize(OpenAIModel.kt:10)
	at com.github.copilot.llmprovider.model.OpenAIChatCompletionRequest$$serializer.deserialize(OpenAIModel.kt:10)
	at kotlinx.serialization.json.internal.StreamingJsonDecoder.decodeSerializableValue(StreamingJsonDecoder.kt:70)
	at kotlinx.serialization.json.Json.decodeFromString(Json.kt:107)
	at com.github.copilot.llmprovider.api.OpenAIApiKt$openAIApi$1.invokeSuspend(OpenAIApi.kt:275)
	at com.github.copilot.llmprovider.api.OpenAIApiKt$openAIApi$1.invoke(OpenAIApi.kt)
	at com.github.copilot.llmprovider.api.OpenAIApiKt$openAIApi$1.invoke(OpenAIApi.kt)
	at io.ktor.server.routing.Route$buildPipeline$1$1.invokeSuspend(Route.kt:116)
	at io.ktor.server.routing.Route$buildPipeline$1$1.invoke(Route.kt)
	at io.ktor.server.routing.Route$buildPipeline$1$1.invoke(Route.kt)
	at io.ktor.util.pipeline.DebugPipelineContext.proceedLoop(DebugPipelineContext.kt:80)
	at io.ktor.util.pipeline.DebugPipelineContext.proceed(DebugPipelineContext.kt:57)
	at io.ktor.util.pipeline.DebugPipelineContext.execute$ktor_utils(DebugPipelineContext.kt:63)
	at io.ktor.util.pipeline.Pipeline.execute(Pipeline.kt:77)
	at io.ktor.server.routing.Routing$executeResult$$inlined$execute$1.invokeSuspend(Pipeline.kt:478)
	at io.ktor.server.routing.Routing$executeResult$$inlined$execute$1.invoke(Pipeline.kt)
	at io.ktor.server.routing.Routing$executeResult$$inlined$execute$1.invoke(Pipeline.kt)
	at io.ktor.util.debug.ContextUtilsKt.initContextInDebugMode(ContextUtils.kt:17)
	at io.ktor.server.routing.Routing.executeResult(Routing.kt:190)
	at io.ktor.server.routing.Routing.interceptor(Routing.kt:64)
	at io.ktor.server.routing.Routing$Plugin$install$1.invokeSuspend(Routing.kt:140)
	at io.ktor.server.routing.Routing$Plugin$install$1.invoke(Routing.kt)
	at io.ktor.server.routing.Routing$Plugin$install$1.invoke(Routing.kt)
	at io.ktor.util.pipeline.DebugPipelineContext.proceedLoop(DebugPipelineContext.kt:80)
	at io.ktor.util.pipeline.DebugPipelineContext.proceed(DebugPipelineContext.kt:57)
	at io.ktor.server.engine.BaseApplicationEngineKt$installDefaultTransformationChecker$1.invokeSuspend(BaseApplicationEngine.kt:124)
	at io.ktor.server.engine.BaseApplicationEngineKt$installDefaultTransformationChecker$1.invoke(BaseApplicationEngine.kt)
	at io.ktor.server.engine.BaseApplicationEngineKt$installDefaultTransformationChecker$1.invoke(BaseApplicationEngine.kt)
	at io.ktor.util.pipeline.DebugPipelineContext.proceedLoop(DebugPipelineContext.kt:80)
	at io.ktor.util.pipeline.DebugPipelineContext.proceed(DebugPipelineContext.kt:57)
	at io.ktor.server.application.hooks.CallFailed$install$1$1.invokeSuspend(CommonHooks.kt:45)
	at io.ktor.server.application.hooks.CallFailed$install$1$1.invoke(CommonHooks.kt)
	at io.ktor.server.application.hooks.CallFailed$install$1$1.invoke(CommonHooks.kt)
	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
	at kotlinx.coroutines.CoroutineScopeKt.coroutineScope(CoroutineScope.kt:264)
	at io.ktor.server.application.hooks.CallFailed$install$1.invokeSuspend(CommonHooks.kt:44)
	at io.ktor.server.application.hooks.CallFailed$install$1.invoke(CommonHooks.kt)
	at io.ktor.server.application.hooks.CallFailed$install$1.invoke(CommonHooks.kt)
	at io.ktor.util.pipeline.DebugPipelineContext.proceedLoop(DebugPipelineContext.kt:80)
	at io.ktor.util.pipeline.DebugPipelineContext.proceed(DebugPipelineContext.kt:57)
	at io.ktor.util.pipeline.DebugPipelineContext.execute$ktor_utils(DebugPipelineContext.kt:63)
	at io.ktor.util.pipeline.Pipeline.execute(Pipeline.kt:77)
	at io.ktor.server.testing.TestApplicationEngine$3$invokeSuspend$$inlined$execute$1.invokeSuspend(Pipeline.kt:478)
	at io.ktor.server.testing.TestApplicationEngine$3$invokeSuspend$$inlined$execute$1.invoke(Pipeline.kt)
	at io.ktor.server.testing.TestApplicationEngine$3$invokeSuspend$$inlined$execute$1.invoke(Pipeline.kt)
	at io.ktor.util.debug.ContextUtilsKt.initContextInDebugMode(ContextUtils.kt:17)
	at io.ktor.server.testing.TestApplicationEngine$3.invokeSuspend(TestApplicationEngine.kt:311)
	at io.ktor.server.testing.TestApplicationEngine$3.invoke(TestApplicationEngine.kt)
	at io.ktor.server.testing.TestApplicationEngine$3.invoke(TestApplicationEngine.kt)
	at io.ktor.server.testing.TestApplicationEngine$2.invokeSuspend(TestApplicationEngine.kt:90)
	at io.ktor.server.testing.TestApplicationEngine$2.invoke(TestApplicationEngine.kt)
	at io.ktor.server.testing.TestApplicationEngine$2.invoke(TestApplicationEngine.kt)
	at io.ktor.util.pipeline.DebugPipelineContext.proceedLoop(DebugPipelineContext.kt:80)
	at io.ktor.util.pipeline.DebugPipelineContext.proceed(DebugPipelineContext.kt:57)
	at io.ktor.util.pipeline.DebugPipelineContext.execute$ktor_utils(DebugPipelineContext.kt:63)
	at io.ktor.util.pipeline.Pipeline.execute(Pipeline.kt:77)
	at io.ktor.server.testing.TestApplicationEngine$handleRequestNonBlocking$2$invokeSuspend$$inlined$execute$1.invokeSuspend(Pipeline.kt:478)
	at io.ktor.server.testing.TestApplicationEngine$handleRequestNonBlocking$2$invokeSuspend$$inlined$execute$1.invoke(Pipeline.kt)
	at io.ktor.server.testing.TestApplicationEngine$handleRequestNonBlocking$2$invokeSuspend$$inlined$execute$1.invoke(Pipeline.kt)
	at io.ktor.util.debug.ContextUtilsKt.initContextInDebugMode(ContextUtils.kt:17)
	at io.ktor.server.testing.TestApplicationEngine$handleRequestNonBlocking$2.invokeSuspend(TestApplicationEngine.kt:310)
	at io.ktor.server.testing.TestApplicationEngine$handleRequestNonBlocking$2.invoke(TestApplicationEngine.kt)
	at io.ktor.server.testing.TestApplicationEngine$handleRequestNonBlocking$2.invoke(TestApplicationEngine.kt)
	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source)
	at io.ktor.server.testing.TestApplicationEngine.handleRequestNonBlocking$ktor_server_test_host(TestApplicationEngine.kt:225)
	at io.ktor.server.testing.TestApplicationEngine.handleRequestNonBlocking$ktor_server_test_host$default(TestApplicationEngine.kt:212)
	at io.ktor.server.testing.client.TestHttpClientEngine.runRequest(TestHttpClientEngine.kt:70)
	at io.ktor.server.testing.client.TestHttpClientEngine.execute(TestHttpClientEngine.kt:55)
	at io.ktor.server.testing.client.DelegatingTestClientEngine.execute(DelegatingTestClientEngine.kt:53)
	at io.ktor.client.engine.HttpClientEngine$executeWithinCallContext$2.invokeSuspend(HttpClientEngine.kt:99)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
13:58:05.089 [DefaultDispatcher-worker-1 @call-context#10] INFO  ktor.test - No ktor.deployment.watch patterns specified, automatic reload is not active.
13:58:05.089 [DefaultDispatcher-worker-1 @call-context#10] INFO  ktor.test - Application started in 0.001 seconds.
13:58:05.089 [DefaultDispatcher-worker-2 @coroutine#12] INFO  ktor.test - Responding at http://localhost:80
13:58:05.089 [DefaultDispatcher-worker-2 @coroutine#12] INFO  ktor.test - Responding at https://localhost:443
13:58:05.093 [DefaultDispatcher-worker-1 @request#10] DEBUG c.g.c.llmprovider.cli.CliMonitor - Logged request: POST /v1/chat/completions -&gt; 200 (0ms)
13:58:06.026 [DefaultDispatcher-worker-1 @request#10] DEBUG c.g.c.llmprovider.cli.CliMonitor - Logged request: POST /v1/chat/completions -&gt; 200 (935ms)
13:58:06.033 [DefaultDispatcher-worker-4 @call-context#18] INFO  ktor.test - No ktor.deployment.watch patterns specified, automatic reload is not active.
13:58:06.034 [DefaultDispatcher-worker-4 @call-context#18] INFO  ktor.test - Application started in 0.001 seconds.
13:58:06.034 [DefaultDispatcher-worker-3 @coroutine#20] INFO  ktor.test - Responding at http://localhost:80
13:58:06.034 [DefaultDispatcher-worker-3 @coroutine#20] INFO  ktor.test - Responding at https://localhost:443
13:58:06.035 [DefaultDispatcher-worker-4 @request#18] DEBUG c.g.c.llmprovider.cli.CliMonitor - Logged request: POST /v1/chat/completions -&gt; 200 (0ms)
13:58:06.037 [DefaultDispatcher-worker-4 @request#18] DEBUG c.g.c.llmprovider.cli.CliMonitor - Logged request: POST /v1/chat/completions -&gt; 200 (3ms)
13:58:06.042 [DefaultDispatcher-worker-4 @call-context#26] INFO  ktor.test - No ktor.deployment.watch patterns specified, automatic reload is not active.
13:58:06.042 [DefaultDispatcher-worker-4 @call-context#26] INFO  ktor.test - Application started in 0.0 seconds.
13:58:06.043 [DefaultDispatcher-worker-1 @coroutine#28] INFO  ktor.test - Responding at http://localhost:80
13:58:06.043 [DefaultDispatcher-worker-1 @coroutine#28] INFO  ktor.test - Responding at https://localhost:443
13:58:06.043 [DefaultDispatcher-worker-4 @request#26] WARN  c.g.c.llmprovider.api.OpenAIApi - Failed to parse OpenAI request
kotlinx.serialization.MissingFieldException: Field 'model' is required for type with serial name 'com.github.copilot.llmprovider.model.OpenAIChatCompletionRequest', but it was missing at path: $
	at kotlinx.serialization.json.internal.StreamingJsonDecoder.decodeSerializableValue(StreamingJsonDecoder.kt:93)
	at kotlinx.serialization.json.Json.decodeFromString(Json.kt:107)
	at com.github.copilot.llmprovider.api.OpenAIApiKt$openAIApi$1.invokeSuspend(OpenAIApi.kt:275)
	at com.github.copilot.llmprovider.api.OpenAIApiKt$openAIApi$1.invoke(OpenAIApi.kt)
	at com.github.copilot.llmprovider.api.OpenAIApiKt$openAIApi$1.invoke(OpenAIApi.kt)
	at io.ktor.server.routing.Route$buildPipeline$1$1.invokeSuspend(Route.kt:116)
	at io.ktor.server.routing.Route$buildPipeline$1$1.invoke(Route.kt)
	at io.ktor.server.routing.Route$buildPipeline$1$1.invoke(Route.kt)
	at io.ktor.util.pipeline.DebugPipelineContext.proceedLoop(DebugPipelineContext.kt:80)
	at io.ktor.util.pipeline.DebugPipelineContext.proceed(DebugPipelineContext.kt:57)
	at io.ktor.util.pipeline.DebugPipelineContext.execute$ktor_utils(DebugPipelineContext.kt:63)
	at io.ktor.util.pipeline.Pipeline.execute(Pipeline.kt:77)
	at io.ktor.server.routing.Routing$executeResult$$inlined$execute$1.invokeSuspend(Pipeline.kt:478)
	at io.ktor.server.routing.Routing$executeResult$$inlined$execute$1.invoke(Pipeline.kt)
	at io.ktor.server.routing.Routing$executeResult$$inlined$execute$1.invoke(Pipeline.kt)
	at io.ktor.util.debug.ContextUtilsKt.initContextInDebugMode(ContextUtils.kt:17)
	at io.ktor.server.routing.Routing.executeResult(Routing.kt:190)
	at io.ktor.server.routing.Routing.interceptor(Routing.kt:64)
	at io.ktor.server.routing.Routing$Plugin$install$1.invokeSuspend(Routing.kt:140)
	at io.ktor.server.routing.Routing$Plugin$install$1.invoke(Routing.kt)
	at io.ktor.server.routing.Routing$Plugin$install$1.invoke(Routing.kt)
	at io.ktor.util.pipeline.DebugPipelineContext.proceedLoop(DebugPipelineContext.kt:80)
	at io.ktor.util.pipeline.DebugPipelineContext.proceed(DebugPipelineContext.kt:57)
	at io.ktor.server.engine.BaseApplicationEngineKt$installDefaultTransformationChecker$1.invokeSuspend(BaseApplicationEngine.kt:124)
	at io.ktor.server.engine.BaseApplicationEngineKt$installDefaultTransformationChecker$1.invoke(BaseApplicationEngine.kt)
	at io.ktor.server.engine.BaseApplicationEngineKt$installDefaultTransformationChecker$1.invoke(BaseApplicationEngine.kt)
	at io.ktor.util.pipeline.DebugPipelineContext.proceedLoop(DebugPipelineContext.kt:80)
	at io.ktor.util.pipeline.DebugPipelineContext.proceed(DebugPipelineContext.kt:57)
	at io.ktor.server.application.hooks.CallFailed$install$1$1.invokeSuspend(CommonHooks.kt:45)
	at io.ktor.server.application.hooks.CallFailed$install$1$1.invoke(CommonHooks.kt)
	at io.ktor.server.application.hooks.CallFailed$install$1$1.invoke(CommonHooks.kt)
	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
	at kotlinx.coroutines.CoroutineScopeKt.coroutineScope(CoroutineScope.kt:264)
	at io.ktor.server.application.hooks.CallFailed$install$1.invokeSuspend(CommonHooks.kt:44)
	at io.ktor.server.application.hooks.CallFailed$install$1.invoke(CommonHooks.kt)
	at io.ktor.server.application.hooks.CallFailed$install$1.invoke(CommonHooks.kt)
	at io.ktor.util.pipeline.DebugPipelineContext.proceedLoop(DebugPipelineContext.kt:80)
	at io.ktor.util.pipeline.DebugPipelineContext.proceed(DebugPipelineContext.kt:57)
	at io.ktor.util.pipeline.DebugPipelineContext.execute$ktor_utils(DebugPipelineContext.kt:63)
	at io.ktor.util.pipeline.Pipeline.execute(Pipeline.kt:77)
	at io.ktor.server.testing.TestApplicationEngine$3$invokeSuspend$$inlined$execute$1.invokeSuspend(Pipeline.kt:478)
	at io.ktor.server.testing.TestApplicationEngine$3$invokeSuspend$$inlined$execute$1.invoke(Pipeline.kt)
	at io.ktor.server.testing.TestApplicationEngine$3$invokeSuspend$$inlined$execute$1.invoke(Pipeline.kt)
	at io.ktor.util.debug.ContextUtilsKt.initContextInDebugMode(ContextUtils.kt:17)
	at io.ktor.server.testing.TestApplicationEngine$3.invokeSuspend(TestApplicationEngine.kt:311)
	at io.ktor.server.testing.TestApplicationEngine$3.invoke(TestApplicationEngine.kt)
	at io.ktor.server.testing.TestApplicationEngine$3.invoke(TestApplicationEngine.kt)
	at io.ktor.server.testing.TestApplicationEngine$2.invokeSuspend(TestApplicationEngine.kt:90)
	at io.ktor.server.testing.TestApplicationEngine$2.invoke(TestApplicationEngine.kt)
	at io.ktor.server.testing.TestApplicationEngine$2.invoke(TestApplicationEngine.kt)
	at io.ktor.util.pipeline.DebugPipelineContext.proceedLoop(DebugPipelineContext.kt:80)
	at io.ktor.util.pipeline.DebugPipelineContext.proceed(DebugPipelineContext.kt:57)
	at io.ktor.util.pipeline.DebugPipelineContext.execute$ktor_utils(DebugPipelineContext.kt:63)
	at io.ktor.util.pipeline.Pipeline.execute(Pipeline.kt:77)
	at io.ktor.server.testing.TestApplicationEngine$handleRequestNonBlocking$2$invokeSuspend$$inlined$execute$1.invokeSuspend(Pipeline.kt:478)
	at io.ktor.server.testing.TestApplicationEngine$handleRequestNonBlocking$2$invokeSuspend$$inlined$execute$1.invoke(Pipeline.kt)
	at io.ktor.server.testing.TestApplicationEngine$handleRequestNonBlocking$2$invokeSuspend$$inlined$execute$1.invoke(Pipeline.kt)
	at io.ktor.util.debug.ContextUtilsKt.initContextInDebugMode(ContextUtils.kt:17)
	at io.ktor.server.testing.TestApplicationEngine$handleRequestNonBlocking$2.invokeSuspend(TestApplicationEngine.kt:310)
	at io.ktor.server.testing.TestApplicationEngine$handleRequestNonBlocking$2.invoke(TestApplicationEngine.kt)
	at io.ktor.server.testing.TestApplicationEngine$handleRequestNonBlocking$2.invoke(TestApplicationEngine.kt)
	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source)
	at io.ktor.server.testing.TestApplicationEngine.handleRequestNonBlocking$ktor_server_test_host(TestApplicationEngine.kt:225)
	at io.ktor.server.testing.TestApplicationEngine.handleRequestNonBlocking$ktor_server_test_host$default(TestApplicationEngine.kt:212)
	at io.ktor.server.testing.client.TestHttpClientEngine.runRequest(TestHttpClientEngine.kt:70)
	at io.ktor.server.testing.client.TestHttpClientEngine.execute(TestHttpClientEngine.kt:55)
	at io.ktor.server.testing.client.DelegatingTestClientEngine.execute(DelegatingTestClientEngine.kt:53)
	at io.ktor.client.engine.HttpClientEngine$executeWithinCallContext$2.invokeSuspend(HttpClientEngine.kt:99)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: kotlinx.serialization.MissingFieldException: Field 'model' is required for type with serial name 'com.github.copilot.llmprovider.model.OpenAIChatCompletionRequest', but it was missing
	at kotlinx.serialization.internal.PluginExceptionsKt.throwMissingFieldException(PluginExceptions.kt:20)
	at com.github.copilot.llmprovider.model.OpenAIChatCompletionRequest.&lt;init&gt;(OpenAIModel.kt:10)
	at com.github.copilot.llmprovider.model.OpenAIChatCompletionRequest$$serializer.deserialize(OpenAIModel.kt:10)
	at com.github.copilot.llmprovider.model.OpenAIChatCompletionRequest$$serializer.deserialize(OpenAIModel.kt:10)
	at kotlinx.serialization.json.internal.StreamingJsonDecoder.decodeSerializableValue(StreamingJsonDecoder.kt:70)
	... 77 common frames omitted
13:58:06.048 [DefaultDispatcher-worker-3 @call-context#34] INFO  ktor.test - No ktor.deployment.watch patterns specified, automatic reload is not active.
13:58:06.049 [DefaultDispatcher-worker-3 @call-context#34] INFO  ktor.test - Application started in 0.001 seconds.
13:58:06.049 [DefaultDispatcher-worker-1 @coroutine#36] INFO  ktor.test - Responding at http://localhost:80
13:58:06.049 [DefaultDispatcher-worker-1 @coroutine#36] INFO  ktor.test - Responding at https://localhost:443
13:58:06.050 [DefaultDispatcher-worker-3 @request#34] DEBUG c.g.c.llmprovider.cli.CliMonitor - Logged request: POST /v1/chat/completions -&gt; 200 (0ms)
13:58:06.050 [DefaultDispatcher-worker-3 @request#34] DEBUG c.g.c.llmprovider.cli.CliMonitor - Logged request: POST /v1/chat/completions -&gt; 200 (1ms)
13:58:06.052 [DefaultDispatcher-worker-4 @call-context#42] INFO  ktor.test - No ktor.deployment.watch patterns specified, automatic reload is not active.
13:58:06.053 [DefaultDispatcher-worker-4 @call-context#42] INFO  ktor.test - Application started in 0.001 seconds.
13:58:06.053 [DefaultDispatcher-worker-2 @coroutine#44] INFO  ktor.test - Responding at http://localhost:80
13:58:06.053 [DefaultDispatcher-worker-2 @coroutine#44] INFO  ktor.test - Responding at https://localhost:443
13:58:06.054 [DefaultDispatcher-worker-4 @request#42] DEBUG c.g.c.llmprovider.cli.CliMonitor - Logged request: POST /v1/chat/completions -&gt; 200 (0ms)
13:58:06.054 [DefaultDispatcher-worker-4 @request#42] DEBUG c.g.c.llmprovider.cli.CliMonitor - Logged request: POST /v1/chat/completions -&gt; 200 (1ms)
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.2</a> at 2025年6月30日 13:58:06</p>
</div>
</div>
</body>
</html>
