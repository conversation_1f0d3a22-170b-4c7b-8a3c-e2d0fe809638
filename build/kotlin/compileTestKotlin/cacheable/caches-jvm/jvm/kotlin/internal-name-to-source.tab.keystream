0com/github/copilot/llmprovider/api/OpenAIApiTestYcom/github/copilot/llmprovider/api/OpenAIApiTest$should handle chat completions request$1[com/github/copilot/llmprovider/api/OpenAIApiTest$should handle chat completions request$1$1ccom/github/copilot/llmprovider/api/OpenAIApiTest$should handle streaming chat completions request$1ecom/github/copilot/llmprovider/api/OpenAIApiTest$should handle streaming chat completions request$1$1]com/github/copilot/llmprovider/api/OpenAIApiTest$should validate required fields in request$1_com/github/copilot/llmprovider/api/OpenAIApiTest$should validate required fields in request$1$1Vcom/github/copilot/llmprovider/api/OpenAIApiTest$should handle tool calls in request$1Xcom/github/copilot/llmprovider/api/OpenAIApiTest$should handle tool calls in request$1$1Xcom/github/copilot/llmprovider/api/OpenAIApiTest$should handle invalid JSON in request$1Zcom/github/copilot/llmprovider/api/OpenAIApiTest$should handle invalid JSON in request$1$1Rcom/github/copilot/llmprovider/api/OpenAIApiTest$should handle unsupported model$1Tcom/github/copilot/llmprovider/api/OpenAIApiTest$should handle unsupported model$1$17com/github/copilot/llmprovider/api/OpenAIApiTest$json$14com/github/copilot/llmprovider/model/ClaudeModelTest;com/github/copilot/llmprovider/model/ClaudeModelTest$json$14com/github/copilot/llmprovider/model/OpenAIModelTestwcom/github/copilot/llmprovider/model/OpenAIModelTest$should validate required fields in request$$inlined$assertThrows$1;com/github/copilot/llmprovider/model/OpenAIModelTest$json$10com/github/copilot/llmprovider/server/ServerTestQcom/github/copilot/llmprovider/server/ServerTest$should respond to health check$1Scom/github/copilot/llmprovider/server/ServerTest$should respond to health check$1$1Xcom/github/copilot/llmprovider/server/ServerTest$should handle CORS preflight requests$1Zcom/github/copilot/llmprovider/server/ServerTest$should handle CORS preflight requests$1$1Zcom/github/copilot/llmprovider/server/ServerTest$should return 404 for unknown endpoints$1\com/github/copilot/llmprovider/server/ServerTest$should return 404 for unknown endpoints$1$1Ycom/github/copilot/llmprovider/server/ServerTest$should handle JSON content negotiation$1[com/github/copilot/llmprovider/server/ServerTest$should handle JSON content negotiation$1$1Tcom/github/copilot/llmprovider/server/ServerTest$should log requests and responses$1Vcom/github/copilot/llmprovider/server/ServerTest$should log requests and responses$1$10com/github/copilot/llmprovider/api/ClaudeApiTestXcom/github/copilot/llmprovider/api/ClaudeApiTest$should handle Claude messages request$1Zcom/github/copilot/llmprovider/api/ClaudeApiTest$should handle Claude messages request$1$1bcom/github/copilot/llmprovider/api/ClaudeApiTest$should handle streaming Claude messages request$1dcom/github/copilot/llmprovider/api/ClaudeApiTest$should handle streaming Claude messages request$1$1dcom/github/copilot/llmprovider/api/ClaudeApiTest$should validate required fields in Claude request$1fcom/github/copilot/llmprovider/api/ClaudeApiTest$should validate required fields in Claude request$1$1acom/github/copilot/llmprovider/api/ClaudeApiTest$should handle system message in Claude request$1ccom/github/copilot/llmprovider/api/ClaudeApiTest$should handle system message in Claude request$1$1_com/github/copilot/llmprovider/api/ClaudeApiTest$should handle invalid JSON in Claude request$1acom/github/copilot/llmprovider/api/ClaudeApiTest$should handle invalid JSON in Claude request$1$1Ycom/github/copilot/llmprovider/api/ClaudeApiTest$should handle unsupported Claude model$1[com/github/copilot/llmprovider/api/ClaudeApiTest$should handle unsupported Claude model$1$1Vcom/github/copilot/llmprovider/api/ClaudeApiTest$should handle max_tokens validation$1Xcom/github/copilot/llmprovider/api/ClaudeApiTest$should handle max_tokens validation$1$17com/github/copilot/llmprovider/api/ClaudeApiTest$json$1                                                                                                                                                                               