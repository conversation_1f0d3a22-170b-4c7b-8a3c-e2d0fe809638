Ecom.github.copilot.llmprovider.model.ClaudeMessageRequest.$serializer>com.github.copilot.llmprovider.model.ClaudeMessage.$serializer<com.github.copilot.llmprovider.model.ClaudeContentBlock.TextHcom.github.copilot.llmprovider.model.ClaudeContentBlock.Text.$serializer?com.github.copilot.llmprovider.model.ClaudeContentBlock.ToolUseKcom.github.copilot.llmprovider.model.ClaudeContentBlock.ToolUse.$serializerBcom.github.copilot.llmprovider.model.ClaudeContentBlock.ToolResultNcom.github.copilot.llmprovider.model.ClaudeContentBlock.ToolResult.$serializer;com.github.copilot.llmprovider.model.ClaudeTool.$serializerFcom.github.copilot.llmprovider.model.ClaudeMessageResponse.$serializer<com.github.copilot.llmprovider.model.ClaudeUsage.$serializerDcom.github.copilot.llmprovider.model.ClaudeErrorResponse.$serializer<com.github.copilot.llmprovider.model.ClaudeError.$serializerLcom.github.copilot.llmprovider.model.OpenAIChatCompletionRequest.$serializer>com.github.copilot.llmprovider.model.OpenAIMessage.$serializer?com.github.copilot.llmprovider.model.OpenAIToolCall.$serializer?com.github.copilot.llmprovider.model.OpenAIFunction.$serializer;com.github.copilot.llmprovider.model.OpenAITool.$serializerIcom.github.copilot.llmprovider.model.OpenAIFunctionDefinition.$serializerMcom.github.copilot.llmprovider.model.OpenAIChatCompletionResponse.$serializer=com.github.copilot.llmprovider.model.OpenAIChoice.$serializer<com.github.copilot.llmprovider.model.OpenAIDelta.$serializer<com.github.copilot.llmprovider.model.OpenAIUsage.$serializerDcom.github.copilot.llmprovider.model.OpenAIErrorResponse.$serializer<com.github.copilot.llmprovider.model.OpenAIError.$serializer7com.github.copilot.llmprovider.service.ProxyServiceImplCcom.github.copilot.llmprovider.model.ClaudeContentBlock.$serializer                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               