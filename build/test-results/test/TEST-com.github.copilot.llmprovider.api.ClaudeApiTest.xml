<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.github.copilot.llmprovider.api.ClaudeApiTest" tests="7" skipped="0" failures="0" errors="0" timestamp="2025-06-30T06:14:35.363Z" hostname="zxnapdeMacBook-Pro.local" time="2.993">
  <properties/>
  <testcase name="should handle max_tokens validation()" classname="com.github.copilot.llmprovider.api.ClaudeApiTest" time="2.002"/>
  <testcase name="should handle Claude messages request()" classname="com.github.copilot.llmprovider.api.ClaudeApiTest" time="0.013"/>
  <testcase name="should handle streaming Claude messages request()" classname="com.github.copilot.llmprovider.api.ClaudeApiTest" time="0.958"/>
  <testcase name="should handle system message in Claude request()" classname="com.github.copilot.llmprovider.api.ClaudeApiTest" time="0.003"/>
  <testcase name="should handle unsupported Claude model()" classname="com.github.copilot.llmprovider.api.ClaudeApiTest" time="0.004"/>
  <testcase name="should validate required fields in Claude request()" classname="com.github.copilot.llmprovider.api.ClaudeApiTest" time="0.006"/>
  <testcase name="should handle invalid JSON in Claude request()" classname="com.github.copilot.llmprovider.api.ClaudeApiTest" time="0.004"/>
  <system-out><![CDATA[14:14:35.576 [DefaultDispatcher-worker-1 @call-context#2] INFO  ktor.test - No ktor.deployment.watch patterns specified, automatic reload is not active.
14:14:37.326 [DefaultDispatcher-worker-1 @call-context#2] INFO  ktor.test - Application started in 1.753 seconds.
14:14:37.327 [DefaultDispatcher-worker-3 @coroutine#4] INFO  ktor.test - Responding at http://localhost:80
14:14:37.328 [DefaultDispatcher-worker-3 @coroutine#4] INFO  ktor.test - Responding at https://localhost:443
14:14:37.368 [DefaultDispatcher-worker-2 @call-context#10] INFO  ktor.test - No ktor.deployment.watch patterns specified, automatic reload is not active.
14:14:37.368 [DefaultDispatcher-worker-2 @call-context#10] INFO  ktor.test - Application started in 0.001 seconds.
14:14:37.368 [DefaultDispatcher-worker-1 @coroutine#12] INFO  ktor.test - Responding at http://localhost:80
14:14:37.368 [DefaultDispatcher-worker-1 @coroutine#12] INFO  ktor.test - Responding at https://localhost:443
14:14:37.371 [DefaultDispatcher-worker-2 @request#10] DEBUG c.g.c.llmprovider.cli.CliMonitor - Logged request: POST /v1/messages -> 200 (0ms)
14:14:37.375 [DefaultDispatcher-worker-2 @request#10] DEBUG c.g.c.llmprovider.cli.CliMonitor - Logged request: POST /v1/messages -> 200 (6ms)
14:14:37.380 [DefaultDispatcher-worker-1 @call-context#18] INFO  ktor.test - No ktor.deployment.watch patterns specified, automatic reload is not active.
14:14:37.381 [DefaultDispatcher-worker-1 @call-context#18] INFO  ktor.test - Application started in 0.001 seconds.
14:14:37.381 [DefaultDispatcher-worker-2 @coroutine#20] INFO  ktor.test - Responding at http://localhost:80
14:14:37.381 [DefaultDispatcher-worker-2 @coroutine#20] INFO  ktor.test - Responding at https://localhost:443
14:14:37.382 [DefaultDispatcher-worker-1 @request#18] DEBUG c.g.c.llmprovider.cli.CliMonitor - Logged request: POST /v1/messages -> 200 (0ms)
14:14:38.337 [DefaultDispatcher-worker-1 @request#18] DEBUG c.g.c.llmprovider.cli.CliMonitor - Logged request: POST /v1/messages -> 200 (956ms)
14:14:38.340 [DefaultDispatcher-worker-1 @call-context#26] INFO  ktor.test - No ktor.deployment.watch patterns specified, automatic reload is not active.
14:14:38.340 [DefaultDispatcher-worker-1 @call-context#26] INFO  ktor.test - Application started in 0.0 seconds.
14:14:38.341 [DefaultDispatcher-worker-4 @coroutine#28] INFO  ktor.test - Responding at http://localhost:80
14:14:38.341 [DefaultDispatcher-worker-4 @coroutine#28] INFO  ktor.test - Responding at https://localhost:443
14:14:38.341 [DefaultDispatcher-worker-1 @request#26] DEBUG c.g.c.llmprovider.cli.CliMonitor - Logged request: POST /v1/messages -> 200 (0ms)
14:14:38.342 [DefaultDispatcher-worker-1 @request#26] DEBUG c.g.c.llmprovider.cli.CliMonitor - Logged request: POST /v1/messages -> 200 (1ms)
14:14:38.344 [DefaultDispatcher-worker-1 @call-context#34] INFO  ktor.test - No ktor.deployment.watch patterns specified, automatic reload is not active.
14:14:38.345 [DefaultDispatcher-worker-1 @call-context#34] INFO  ktor.test - Application started in 0.001 seconds.
14:14:38.345 [DefaultDispatcher-worker-3 @coroutine#36] INFO  ktor.test - Responding at http://localhost:80
14:14:38.345 [DefaultDispatcher-worker-3 @coroutine#36] INFO  ktor.test - Responding at https://localhost:443
14:14:38.345 [DefaultDispatcher-worker-1 @request#34] DEBUG c.g.c.llmprovider.cli.CliMonitor - Logged request: POST /v1/messages -> 200 (0ms)
14:14:38.346 [DefaultDispatcher-worker-1 @request#34] DEBUG c.g.c.llmprovider.cli.CliMonitor - Logged request: POST /v1/messages -> 200 (1ms)
14:14:38.348 [DefaultDispatcher-worker-3 @call-context#42] INFO  ktor.test - No ktor.deployment.watch patterns specified, automatic reload is not active.
14:14:38.349 [DefaultDispatcher-worker-3 @call-context#42] INFO  ktor.test - Application started in 0.0 seconds.
14:14:38.349 [DefaultDispatcher-worker-4 @coroutine#44] INFO  ktor.test - Responding at http://localhost:80
14:14:38.349 [DefaultDispatcher-worker-4 @coroutine#44] INFO  ktor.test - Responding at https://localhost:443
14:14:38.350 [DefaultDispatcher-worker-3 @request#42] WARN  c.g.c.llmprovider.api.ClaudeApi - Failed to parse Claude request
kotlinx.serialization.MissingFieldException: Fields [model, max_tokens] are required for type with serial name 'com.github.copilot.llmprovider.model.ClaudeMessageRequest', but they were missing at path: $
	at kotlinx.serialization.json.internal.StreamingJsonDecoder.decodeSerializableValue(StreamingJsonDecoder.kt:93)
	at kotlinx.serialization.json.Json.decodeFromString(Json.kt:107)
	at com.github.copilot.llmprovider.api.ClaudeApiKt$claudeApi$1.invokeSuspend(ClaudeApi.kt:280)
	at com.github.copilot.llmprovider.api.ClaudeApiKt$claudeApi$1.invoke(ClaudeApi.kt)
	at com.github.copilot.llmprovider.api.ClaudeApiKt$claudeApi$1.invoke(ClaudeApi.kt)
	at io.ktor.server.routing.Route$buildPipeline$1$1.invokeSuspend(Route.kt:116)
	at io.ktor.server.routing.Route$buildPipeline$1$1.invoke(Route.kt)
	at io.ktor.server.routing.Route$buildPipeline$1$1.invoke(Route.kt)
	at io.ktor.util.pipeline.DebugPipelineContext.proceedLoop(DebugPipelineContext.kt:80)
	at io.ktor.util.pipeline.DebugPipelineContext.proceed(DebugPipelineContext.kt:57)
	at io.ktor.util.pipeline.DebugPipelineContext.execute$ktor_utils(DebugPipelineContext.kt:63)
	at io.ktor.util.pipeline.Pipeline.execute(Pipeline.kt:77)
	at io.ktor.server.routing.Routing$executeResult$$inlined$execute$1.invokeSuspend(Pipeline.kt:478)
	at io.ktor.server.routing.Routing$executeResult$$inlined$execute$1.invoke(Pipeline.kt)
	at io.ktor.server.routing.Routing$executeResult$$inlined$execute$1.invoke(Pipeline.kt)
	at io.ktor.util.debug.ContextUtilsKt.initContextInDebugMode(ContextUtils.kt:17)
	at io.ktor.server.routing.Routing.executeResult(Routing.kt:190)
	at io.ktor.server.routing.Routing.interceptor(Routing.kt:64)
	at io.ktor.server.routing.Routing$Plugin$install$1.invokeSuspend(Routing.kt:140)
	at io.ktor.server.routing.Routing$Plugin$install$1.invoke(Routing.kt)
	at io.ktor.server.routing.Routing$Plugin$install$1.invoke(Routing.kt)
	at io.ktor.util.pipeline.DebugPipelineContext.proceedLoop(DebugPipelineContext.kt:80)
	at io.ktor.util.pipeline.DebugPipelineContext.proceed(DebugPipelineContext.kt:57)
	at io.ktor.server.engine.BaseApplicationEngineKt$installDefaultTransformationChecker$1.invokeSuspend(BaseApplicationEngine.kt:124)
	at io.ktor.server.engine.BaseApplicationEngineKt$installDefaultTransformationChecker$1.invoke(BaseApplicationEngine.kt)
	at io.ktor.server.engine.BaseApplicationEngineKt$installDefaultTransformationChecker$1.invoke(BaseApplicationEngine.kt)
	at io.ktor.util.pipeline.DebugPipelineContext.proceedLoop(DebugPipelineContext.kt:80)
	at io.ktor.util.pipeline.DebugPipelineContext.proceed(DebugPipelineContext.kt:57)
	at io.ktor.server.application.hooks.CallFailed$install$1$1.invokeSuspend(CommonHooks.kt:45)
	at io.ktor.server.application.hooks.CallFailed$install$1$1.invoke(CommonHooks.kt)
	at io.ktor.server.application.hooks.CallFailed$install$1$1.invoke(CommonHooks.kt)
	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
	at kotlinx.coroutines.CoroutineScopeKt.coroutineScope(CoroutineScope.kt:264)
	at io.ktor.server.application.hooks.CallFailed$install$1.invokeSuspend(CommonHooks.kt:44)
	at io.ktor.server.application.hooks.CallFailed$install$1.invoke(CommonHooks.kt)
	at io.ktor.server.application.hooks.CallFailed$install$1.invoke(CommonHooks.kt)
	at io.ktor.util.pipeline.DebugPipelineContext.proceedLoop(DebugPipelineContext.kt:80)
	at io.ktor.util.pipeline.DebugPipelineContext.proceed(DebugPipelineContext.kt:57)
	at io.ktor.util.pipeline.DebugPipelineContext.execute$ktor_utils(DebugPipelineContext.kt:63)
	at io.ktor.util.pipeline.Pipeline.execute(Pipeline.kt:77)
	at io.ktor.server.testing.TestApplicationEngine$3$invokeSuspend$$inlined$execute$1.invokeSuspend(Pipeline.kt:478)
	at io.ktor.server.testing.TestApplicationEngine$3$invokeSuspend$$inlined$execute$1.invoke(Pipeline.kt)
	at io.ktor.server.testing.TestApplicationEngine$3$invokeSuspend$$inlined$execute$1.invoke(Pipeline.kt)
	at io.ktor.util.debug.ContextUtilsKt.initContextInDebugMode(ContextUtils.kt:17)
	at io.ktor.server.testing.TestApplicationEngine$3.invokeSuspend(TestApplicationEngine.kt:311)
	at io.ktor.server.testing.TestApplicationEngine$3.invoke(TestApplicationEngine.kt)
	at io.ktor.server.testing.TestApplicationEngine$3.invoke(TestApplicationEngine.kt)
	at io.ktor.server.testing.TestApplicationEngine$2.invokeSuspend(TestApplicationEngine.kt:90)
	at io.ktor.server.testing.TestApplicationEngine$2.invoke(TestApplicationEngine.kt)
	at io.ktor.server.testing.TestApplicationEngine$2.invoke(TestApplicationEngine.kt)
	at io.ktor.util.pipeline.DebugPipelineContext.proceedLoop(DebugPipelineContext.kt:80)
	at io.ktor.util.pipeline.DebugPipelineContext.proceed(DebugPipelineContext.kt:57)
	at io.ktor.util.pipeline.DebugPipelineContext.execute$ktor_utils(DebugPipelineContext.kt:63)
	at io.ktor.util.pipeline.Pipeline.execute(Pipeline.kt:77)
	at io.ktor.server.testing.TestApplicationEngine$handleRequestNonBlocking$2$invokeSuspend$$inlined$execute$1.invokeSuspend(Pipeline.kt:478)
	at io.ktor.server.testing.TestApplicationEngine$handleRequestNonBlocking$2$invokeSuspend$$inlined$execute$1.invoke(Pipeline.kt)
	at io.ktor.server.testing.TestApplicationEngine$handleRequestNonBlocking$2$invokeSuspend$$inlined$execute$1.invoke(Pipeline.kt)
	at io.ktor.util.debug.ContextUtilsKt.initContextInDebugMode(ContextUtils.kt:17)
	at io.ktor.server.testing.TestApplicationEngine$handleRequestNonBlocking$2.invokeSuspend(TestApplicationEngine.kt:310)
	at io.ktor.server.testing.TestApplicationEngine$handleRequestNonBlocking$2.invoke(TestApplicationEngine.kt)
	at io.ktor.server.testing.TestApplicationEngine$handleRequestNonBlocking$2.invoke(TestApplicationEngine.kt)
	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source)
	at io.ktor.server.testing.TestApplicationEngine.handleRequestNonBlocking$ktor_server_test_host(TestApplicationEngine.kt:225)
	at io.ktor.server.testing.TestApplicationEngine.handleRequestNonBlocking$ktor_server_test_host$default(TestApplicationEngine.kt:212)
	at io.ktor.server.testing.client.TestHttpClientEngine.runRequest(TestHttpClientEngine.kt:70)
	at io.ktor.server.testing.client.TestHttpClientEngine.execute(TestHttpClientEngine.kt:55)
	at io.ktor.server.testing.client.DelegatingTestClientEngine.execute(DelegatingTestClientEngine.kt:53)
	at io.ktor.client.engine.HttpClientEngine$executeWithinCallContext$2.invokeSuspend(HttpClientEngine.kt:99)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
Caused by: kotlinx.serialization.MissingFieldException: Fields [model, max_tokens] are required for type with serial name 'com.github.copilot.llmprovider.model.ClaudeMessageRequest', but they were missing
	at kotlinx.serialization.internal.PluginExceptionsKt.throwMissingFieldException(PluginExceptions.kt:20)
	at com.github.copilot.llmprovider.model.ClaudeMessageRequest.<init>(ClaudeModel.kt:11)
	at com.github.copilot.llmprovider.model.ClaudeMessageRequest$$serializer.deserialize(ClaudeModel.kt:11)
	at com.github.copilot.llmprovider.model.ClaudeMessageRequest$$serializer.deserialize(ClaudeModel.kt:11)
	at kotlinx.serialization.json.internal.StreamingJsonDecoder.decodeSerializableValue(StreamingJsonDecoder.kt:70)
	... 77 common frames omitted
14:14:38.354 [DefaultDispatcher-worker-4 @call-context#50] INFO  ktor.test - No ktor.deployment.watch patterns specified, automatic reload is not active.
14:14:38.354 [DefaultDispatcher-worker-4 @call-context#50] INFO  ktor.test - Application started in 0.0 seconds.
14:14:38.355 [DefaultDispatcher-worker-3 @coroutine#52] INFO  ktor.test - Responding at http://localhost:80
14:14:38.355 [DefaultDispatcher-worker-3 @coroutine#52] INFO  ktor.test - Responding at https://localhost:443
14:14:38.355 [DefaultDispatcher-worker-4 @request#50] WARN  c.g.c.llmprovider.api.ClaudeApi - Failed to parse Claude request
kotlinx.serialization.json.internal.JsonDecodingException: Unexpected JSON token at offset 0: Expected start of the object '{', but had 'i' instead at path: $
JSON input: invalid json
	at kotlinx.serialization.json.internal.JsonExceptionsKt.JsonDecodingException(JsonExceptions.kt:24)
	at kotlinx.serialization.json.internal.JsonExceptionsKt.JsonDecodingException(JsonExceptions.kt:32)
	at kotlinx.serialization.json.internal.AbstractJsonLexer.fail(AbstractJsonLexer.kt:598)
	at kotlinx.serialization.json.internal.AbstractJsonLexer.fail$default(AbstractJsonLexer.kt:596)
	at kotlinx.serialization.json.internal.AbstractJsonLexer.fail$kotlinx_serialization_json(AbstractJsonLexer.kt:233)
	at kotlinx.serialization.json.internal.AbstractJsonLexer.fail$kotlinx_serialization_json$default(AbstractJsonLexer.kt:228)
	at kotlinx.serialization.json.internal.AbstractJsonLexer.unexpectedToken(AbstractJsonLexer.kt:225)
	at kotlinx.serialization.json.internal.StringJsonLexer.consumeNextToken(StringJsonLexer.kt:74)
	at kotlinx.serialization.json.internal.StreamingJsonDecoder.beginStructure(StreamingJsonDecoder.kt:100)
	at com.github.copilot.llmprovider.model.ClaudeMessageRequest$$serializer.deserialize(ClaudeModel.kt:11)
	at com.github.copilot.llmprovider.model.ClaudeMessageRequest$$serializer.deserialize(ClaudeModel.kt:11)
	at kotlinx.serialization.json.internal.StreamingJsonDecoder.decodeSerializableValue(StreamingJsonDecoder.kt:70)
	at kotlinx.serialization.json.Json.decodeFromString(Json.kt:107)
	at com.github.copilot.llmprovider.api.ClaudeApiKt$claudeApi$1.invokeSuspend(ClaudeApi.kt:280)
	at com.github.copilot.llmprovider.api.ClaudeApiKt$claudeApi$1.invoke(ClaudeApi.kt)
	at com.github.copilot.llmprovider.api.ClaudeApiKt$claudeApi$1.invoke(ClaudeApi.kt)
	at io.ktor.server.routing.Route$buildPipeline$1$1.invokeSuspend(Route.kt:116)
	at io.ktor.server.routing.Route$buildPipeline$1$1.invoke(Route.kt)
	at io.ktor.server.routing.Route$buildPipeline$1$1.invoke(Route.kt)
	at io.ktor.util.pipeline.DebugPipelineContext.proceedLoop(DebugPipelineContext.kt:80)
	at io.ktor.util.pipeline.DebugPipelineContext.proceed(DebugPipelineContext.kt:57)
	at io.ktor.util.pipeline.DebugPipelineContext.execute$ktor_utils(DebugPipelineContext.kt:63)
	at io.ktor.util.pipeline.Pipeline.execute(Pipeline.kt:77)
	at io.ktor.server.routing.Routing$executeResult$$inlined$execute$1.invokeSuspend(Pipeline.kt:478)
	at io.ktor.server.routing.Routing$executeResult$$inlined$execute$1.invoke(Pipeline.kt)
	at io.ktor.server.routing.Routing$executeResult$$inlined$execute$1.invoke(Pipeline.kt)
	at io.ktor.util.debug.ContextUtilsKt.initContextInDebugMode(ContextUtils.kt:17)
	at io.ktor.server.routing.Routing.executeResult(Routing.kt:190)
	at io.ktor.server.routing.Routing.interceptor(Routing.kt:64)
	at io.ktor.server.routing.Routing$Plugin$install$1.invokeSuspend(Routing.kt:140)
	at io.ktor.server.routing.Routing$Plugin$install$1.invoke(Routing.kt)
	at io.ktor.server.routing.Routing$Plugin$install$1.invoke(Routing.kt)
	at io.ktor.util.pipeline.DebugPipelineContext.proceedLoop(DebugPipelineContext.kt:80)
	at io.ktor.util.pipeline.DebugPipelineContext.proceed(DebugPipelineContext.kt:57)
	at io.ktor.server.engine.BaseApplicationEngineKt$installDefaultTransformationChecker$1.invokeSuspend(BaseApplicationEngine.kt:124)
	at io.ktor.server.engine.BaseApplicationEngineKt$installDefaultTransformationChecker$1.invoke(BaseApplicationEngine.kt)
	at io.ktor.server.engine.BaseApplicationEngineKt$installDefaultTransformationChecker$1.invoke(BaseApplicationEngine.kt)
	at io.ktor.util.pipeline.DebugPipelineContext.proceedLoop(DebugPipelineContext.kt:80)
	at io.ktor.util.pipeline.DebugPipelineContext.proceed(DebugPipelineContext.kt:57)
	at io.ktor.server.application.hooks.CallFailed$install$1$1.invokeSuspend(CommonHooks.kt:45)
	at io.ktor.server.application.hooks.CallFailed$install$1$1.invoke(CommonHooks.kt)
	at io.ktor.server.application.hooks.CallFailed$install$1$1.invoke(CommonHooks.kt)
	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
	at kotlinx.coroutines.CoroutineScopeKt.coroutineScope(CoroutineScope.kt:264)
	at io.ktor.server.application.hooks.CallFailed$install$1.invokeSuspend(CommonHooks.kt:44)
	at io.ktor.server.application.hooks.CallFailed$install$1.invoke(CommonHooks.kt)
	at io.ktor.server.application.hooks.CallFailed$install$1.invoke(CommonHooks.kt)
	at io.ktor.util.pipeline.DebugPipelineContext.proceedLoop(DebugPipelineContext.kt:80)
	at io.ktor.util.pipeline.DebugPipelineContext.proceed(DebugPipelineContext.kt:57)
	at io.ktor.util.pipeline.DebugPipelineContext.execute$ktor_utils(DebugPipelineContext.kt:63)
	at io.ktor.util.pipeline.Pipeline.execute(Pipeline.kt:77)
	at io.ktor.server.testing.TestApplicationEngine$3$invokeSuspend$$inlined$execute$1.invokeSuspend(Pipeline.kt:478)
	at io.ktor.server.testing.TestApplicationEngine$3$invokeSuspend$$inlined$execute$1.invoke(Pipeline.kt)
	at io.ktor.server.testing.TestApplicationEngine$3$invokeSuspend$$inlined$execute$1.invoke(Pipeline.kt)
	at io.ktor.util.debug.ContextUtilsKt.initContextInDebugMode(ContextUtils.kt:17)
	at io.ktor.server.testing.TestApplicationEngine$3.invokeSuspend(TestApplicationEngine.kt:311)
	at io.ktor.server.testing.TestApplicationEngine$3.invoke(TestApplicationEngine.kt)
	at io.ktor.server.testing.TestApplicationEngine$3.invoke(TestApplicationEngine.kt)
	at io.ktor.server.testing.TestApplicationEngine$2.invokeSuspend(TestApplicationEngine.kt:90)
	at io.ktor.server.testing.TestApplicationEngine$2.invoke(TestApplicationEngine.kt)
	at io.ktor.server.testing.TestApplicationEngine$2.invoke(TestApplicationEngine.kt)
	at io.ktor.util.pipeline.DebugPipelineContext.proceedLoop(DebugPipelineContext.kt:80)
	at io.ktor.util.pipeline.DebugPipelineContext.proceed(DebugPipelineContext.kt:57)
	at io.ktor.util.pipeline.DebugPipelineContext.execute$ktor_utils(DebugPipelineContext.kt:63)
	at io.ktor.util.pipeline.Pipeline.execute(Pipeline.kt:77)
	at io.ktor.server.testing.TestApplicationEngine$handleRequestNonBlocking$2$invokeSuspend$$inlined$execute$1.invokeSuspend(Pipeline.kt:478)
	at io.ktor.server.testing.TestApplicationEngine$handleRequestNonBlocking$2$invokeSuspend$$inlined$execute$1.invoke(Pipeline.kt)
	at io.ktor.server.testing.TestApplicationEngine$handleRequestNonBlocking$2$invokeSuspend$$inlined$execute$1.invoke(Pipeline.kt)
	at io.ktor.util.debug.ContextUtilsKt.initContextInDebugMode(ContextUtils.kt:17)
	at io.ktor.server.testing.TestApplicationEngine$handleRequestNonBlocking$2.invokeSuspend(TestApplicationEngine.kt:310)
	at io.ktor.server.testing.TestApplicationEngine$handleRequestNonBlocking$2.invoke(TestApplicationEngine.kt)
	at io.ktor.server.testing.TestApplicationEngine$handleRequestNonBlocking$2.invoke(TestApplicationEngine.kt)
	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source)
	at io.ktor.server.testing.TestApplicationEngine.handleRequestNonBlocking$ktor_server_test_host(TestApplicationEngine.kt:225)
	at io.ktor.server.testing.TestApplicationEngine.handleRequestNonBlocking$ktor_server_test_host$default(TestApplicationEngine.kt:212)
	at io.ktor.server.testing.client.TestHttpClientEngine.runRequest(TestHttpClientEngine.kt:70)
	at io.ktor.server.testing.client.TestHttpClientEngine.execute(TestHttpClientEngine.kt:55)
	at io.ktor.server.testing.client.DelegatingTestClientEngine.execute(DelegatingTestClientEngine.kt:53)
	at io.ktor.client.engine.HttpClientEngine$executeWithinCallContext$2.invokeSuspend(HttpClientEngine.kt:99)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:103)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
