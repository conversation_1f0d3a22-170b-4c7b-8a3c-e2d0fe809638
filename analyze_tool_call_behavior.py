#!/usr/bin/env python3

import json
import requests
import time

def analyze_tool_call_behavior():
    """分析工具调用行为，特别是重复调用的问题"""
    print("🔍 Analyzing Tool Call Behavior")
    print("━" * 60)
    
    base_url = "http://localhost:8080"
    
    # 检查服务器状态
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print("⚠️  Server responded but may not be healthy")
            return False
    except:
        print("❌ Server is not running. Please start it first:")
        print("   ./run_debug_test.sh")
        return False
    
    print("━" * 60)
    
    # 测试 1: 简单的工具调用（第一轮）
    print("📝 Test 1: Initial tool call")
    
    initial_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 150,
        "messages": [
            {
                "role": "user",
                "content": "List the files in the current directory."
            }
        ],
        "tools": [
            {
                "name": "LS",
                "description": "List files and directories",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "path": {
                            "type": "string",
                            "description": "Directory path to list"
                        }
                    },
                    "required": ["path"]
                }
            }
        ]
    }
    
    print("🚀 Sending initial request...")
    response1 = send_request(base_url, initial_request)
    
    if not response1:
        print("❌ Initial request failed")
        return False
    
    # 分析第一次响应
    tool_use_blocks = [block for block in response1.get('content', []) if block.get('type') == 'tool_use']
    
    if not tool_use_blocks:
        print("❌ No tool_use blocks in initial response")
        return False
    
    first_tool_use = tool_use_blocks[0]
    tool_id = first_tool_use.get('id')
    tool_name = first_tool_use.get('name')
    tool_input = first_tool_use.get('input', {})
    
    print(f"✅ AI returned tool call: {tool_name}")
    print(f"   ID: {tool_id}")
    print(f"   Input: {json.dumps(tool_input)}")
    
    # 测试 2: 模拟工具执行结果（第二轮）
    print("\n📝 Test 2: Tool result response")
    
    # 模拟工具执行结果
    tool_result_content = """Files in directory:
- file1.txt
- file2.py
- folder1/
- folder2/"""
    
    followup_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 150,
        "messages": [
            {
                "role": "user",
                "content": "List the files in the current directory."
            },
            {
                "role": "assistant",
                "content": [
                    {
                        "type": "tool_use",
                        "id": tool_id,
                        "name": tool_name,
                        "input": tool_input
                    }
                ]
            },
            {
                "role": "user",
                "content": [
                    {
                        "type": "tool_result",
                        "tool_use_id": tool_id,
                        "content": tool_result_content
                    }
                ]
            }
        ],
        "tools": [
            {
                "name": "LS",
                "description": "List files and directories",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "path": {
                            "type": "string",
                            "description": "Directory path to list"
                        }
                    },
                    "required": ["path"]
                }
            }
        ]
    }
    
    print("🚀 Sending follow-up request with tool result...")
    response2 = send_request(base_url, followup_request)
    
    if not response2:
        print("❌ Follow-up request failed")
        return False
    
    # 分析第二次响应
    print("\n🔍 Analyzing follow-up response:")
    
    content_blocks = response2.get('content', [])
    tool_use_blocks_2 = [block for block in content_blocks if block.get('type') == 'tool_use']
    text_blocks_2 = [block for block in content_blocks if block.get('type') == 'text']
    
    print(f"  📋 Content blocks: {len(content_blocks)}")
    print(f"  🔧 Tool use blocks: {len(tool_use_blocks_2)}")
    print(f"  📝 Text blocks: {len(text_blocks_2)}")
    
    if tool_use_blocks_2:
        print("  ⚠️  AI returned MORE tool calls after seeing tool result!")
        for i, tool_block in enumerate(tool_use_blocks_2):
            print(f"    {i+1}. Tool: {tool_block.get('name')}")
            print(f"       ID: {tool_block.get('id')}")
            print(f"       Input: {json.dumps(tool_block.get('input', {}))}")
        
        # 检查是否是相同的工具调用
        if (tool_use_blocks_2[0].get('name') == tool_name and 
            tool_use_blocks_2[0].get('input') == tool_input):
            print("  🔄 REPEATED TOOL CALL DETECTED!")
            print("  This is the same tool call as before.")
        else:
            print("  🆕 Different tool call - AI is exploring further.")
    
    if text_blocks_2:
        print("  ✅ AI also provided text response:")
        for i, text_block in enumerate(text_blocks_2):
            text = text_block.get('text', '')
            print(f"    {i+1}. {text[:100]}{'...' if len(text) > 100 else ''}")
    
    # 分析结果
    print("\n📊 Behavior Analysis:")
    
    if tool_use_blocks_2 and not text_blocks_2:
        print("  🔄 ISSUE: AI only returned tool calls, no final answer")
        print("  This suggests the AI thinks it needs more information")
        print("  or there's a loop in the tool calling logic.")
        
    elif tool_use_blocks_2 and text_blocks_2:
        print("  ⚠️  MIXED: AI returned both tool calls and text")
        print("  This might be normal behavior for complex tasks.")
        
    elif not tool_use_blocks_2 and text_blocks_2:
        print("  ✅ GOOD: AI provided final answer based on tool result")
        print("  This is the expected behavior after tool execution.")
        
    else:
        print("  ❌ UNEXPECTED: No content in response")
    
    return True

def send_request(base_url, request_data):
    """发送请求并返回响应数据"""
    try:
        response = requests.post(
            f"{base_url}/v1/messages",
            json=request_data,
            timeout=60
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"📄 Error: {response.text[:200]}")
            return None
            
    except Exception as e:
        print(f"❌ Request error: {e}")
        return None

if __name__ == "__main__":
    print("🚀 Tool Call Behavior Analysis")
    print("This test analyzes whether GitHub Copilot API")
    print("repeats tool calls after seeing tool results.")
    print("━" * 60)
    
    success = analyze_tool_call_behavior()
    
    print("━" * 60)
    if success:
        print("🎉 ANALYSIS COMPLETED!")
        print("\n💡 Understanding Tool Call Behavior:")
        print("  • If AI repeats tool calls: This is GitHub Copilot's decision")
        print("  • If AI gives final answer: Normal expected behavior")
        print("  • Our proxy correctly converts formats in both cases")
        print("\n🔧 The 'repeated tool calls' issue is likely:")
        print("  1. GitHub Copilot API's AI model behavior")
        print("  2. Not a bug in our format conversion")
        print("  3. The AI thinks it needs more/different information")
    else:
        print("❌ Analysis failed.")
        print("Check server logs for issues.")
