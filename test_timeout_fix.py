#!/usr/bin/env python3

import json
import requests
import time

def test_timeout_fix():
    """测试超时修复 - 验证长时间运行的请求不会超时"""
    print("⏰ Testing GitHub Copilot API Timeout Fix")
    print("━" * 60)
    
    base_url = "http://localhost:8080"
    
    print("🔧 Timeout Configuration:")
    print("  - Request timeout: 10 minutes")
    print("  - Connect timeout: 30 seconds")
    print("  - Socket timeout: 10 minutes")
    print("")
    print("🎯 This test verifies that complex coding requests")
    print("   don't fail with 'Request timeout has expired' errors")
    print("")
    
    # 测试 1: 简单请求（基线测试）
    print("📝 Test 1: Simple request (baseline)")
    simple_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 100,
        "messages": [
            {
                "role": "user",
                "content": "Hello! Please respond briefly."
            }
        ]
    }
    
    success1 = test_request(base_url, simple_request, "Simple Request", expected_time=5)
    
    if not success1:
        print("❌ Baseline test failed")
        return False
    
    # 测试 2: 复杂代码生成请求（可能需要更长时间）
    print("\n📝 Test 2: Complex code generation request")
    complex_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 500,
        "system": "You are an expert software engineer. Provide detailed, well-commented code.",
        "messages": [
            {
                "role": "user",
                "content": """Please create a complete Python web scraper that:
1. Uses requests and BeautifulSoup
2. Handles rate limiting with exponential backoff
3. Includes proper error handling and logging
4. Has a configurable user agent rotation
5. Supports both synchronous and asynchronous operations
6. Includes comprehensive docstrings and type hints
7. Has unit tests using pytest

Make it production-ready with proper exception handling."""
            }
        ],
        "temperature": 0.3
    }
    
    success2 = test_request(base_url, complex_request, "Complex Code Generation", expected_time=30)
    
    # 测试 3: 长对话请求
    print("\n📝 Test 3: Long conversation request")
    long_conversation_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 400,
        "messages": [
            {
                "role": "user",
                "content": "I'm building a microservices architecture. Can you help me design it?"
            },
            {
                "role": "assistant",
                "content": "I'd be happy to help you design a microservices architecture! To provide the best guidance, I need to understand your specific requirements."
            },
            {
                "role": "user",
                "content": "It's for an e-commerce platform with user management, product catalog, inventory, orders, payments, and notifications."
            },
            {
                "role": "assistant",
                "content": "Great! For an e-commerce platform, I recommend breaking it down into these core microservices: User Service, Product Catalog Service, Inventory Service, Order Service, Payment Service, and Notification Service."
            },
            {
                "role": "user",
                "content": "How should these services communicate? What about data consistency and transaction management across services?"
            }
        ],
        "temperature": 0.4
    }
    
    success3 = test_request(base_url, long_conversation_request, "Long Conversation", expected_time=25)
    
    # 测试 4: 多个连续请求（测试持续性能）
    print("\n📝 Test 4: Multiple consecutive requests")
    consecutive_success = True
    
    for i in range(3):
        print(f"\n🔄 Consecutive request {i+1}/3:")
        consecutive_request = {
            "model": "claude-3.5-sonnet",
            "max_tokens": 200,
            "messages": [
                {
                    "role": "user",
                    "content": f"Request #{i+1}: Explain the benefits of microservices architecture in {50 + i*20} words."
                }
            ]
        }
        
        success = test_request(base_url, consecutive_request, f"Consecutive {i+1}", expected_time=15)
        if not success:
            consecutive_success = False
        
        time.sleep(2)  # 短暂间隔
    
    print("\n━" * 60)
    print("📊 Timeout Fix Test Results:")
    print(f"  ✅ Simple request: {'PASS' if success1 else 'FAIL'}")
    print(f"  ✅ Complex code generation: {'PASS' if success2 else 'FAIL'}")
    print(f"  ✅ Long conversation: {'PASS' if success3 else 'FAIL'}")
    print(f"  ✅ Consecutive requests: {'PASS' if consecutive_success else 'FAIL'}")
    
    all_success = success1 and success2 and success3 and consecutive_success
    
    if all_success:
        print("\n🎉 All timeout tests passed!")
        print("✅ GitHub Copilot API timeout issues have been resolved!")
        print("✅ Complex coding requests can now complete successfully!")
    else:
        print("\n❌ Some timeout tests failed.")
        print("Check server logs for timeout-related errors.")
    
    return all_success

def test_request(base_url, request_data, test_name, expected_time=10):
    """测试单个请求，返回是否成功"""
    print(f"🚀 Starting {test_name}...")
    start_time = time.time()
    
    try:
        response = requests.post(
            f"{base_url}/v1/messages",
            json=request_data,
            timeout=12 * 60  # 客户端 12 分钟超时（比服务器的 10 分钟稍长）
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"📊 Status: {response.status_code}")
        print(f"⏱️  Duration: {duration:.1f} seconds")
        
        if response.status_code == 200:
            print("✅ Success!")
            try:
                response_data = response.json()
                content = response_data.get('content', [])
                if content and len(content) > 0:
                    text = content[0].get('text', '')
                    print(f"📏 Response length: {len(text)} characters")
                    print(f"🤖 Response preview: {text[:80]}{'...' if len(text) > 80 else ''}")
                    
                    # 检查响应质量
                    if len(text.strip()) > 20:
                        print("✅ Good response quality!")
                        return True
                    else:
                        print("⚠️  Response too short")
                        return False
                else:
                    print("⚠️  No content in response")
                    return False
            except json.JSONDecodeError:
                print("❌ Invalid JSON response")
                return False
                
        elif response.status_code == 500:
            print("❌ Server error (500)")
            error_text = response.text
            
            # 检查是否是超时错误
            if "timeout" in error_text.lower():
                print("🚨 TIMEOUT ERROR DETECTED!")
                print("   This indicates the timeout fix may not be working")
                print(f"   Error: {error_text[:200]}")
                return False
            else:
                print("⚠️  Other server error (not timeout related)")
                print(f"   Error: {error_text[:150]}")
                return False
                
        else:
            print(f"⚠️  Unexpected status: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        end_time = time.time()
        duration = end_time - start_time
        print(f"⏰ CLIENT timeout after {duration:.1f} seconds")
        print("   This suggests the server timeout is working (> 10 minutes)")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - make sure server is running")
        return False
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        print(f"❌ Unexpected error after {duration:.1f} seconds: {e}")
        return False

if __name__ == "__main__":
    print("🚀 GitHub Copilot API Timeout Fix Test")
    print("Make sure the server is running on http://localhost:8080")
    print("━" * 60)
    print("This test verifies that the timeout configuration fixes:")
    print("  ❌ 'Request timeout has expired' errors")
    print("  ❌ Premature request termination")
    print("  ✅ Complex coding requests can complete")
    print("  ✅ Long-running AI operations succeed")
    print("━" * 60)
    
    # 检查服务器状态
    try:
        response = requests.get("http://localhost:8080/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print("⚠️  Server responded but may not be healthy")
    except:
        print("❌ Server is not running. Please start it first:")
        print("   ./run_debug_test.sh")
        exit(1)
    
    print("━" * 60)
    
    # 运行测试
    success = test_timeout_fix()
    
    print("━" * 60)
    if success:
        print("🎉 TIMEOUT FIX TEST PASSED!")
        print("GitHub Copilot API timeout issues have been resolved!")
    else:
        print("❌ Timeout fix test failed.")
        print("Check server logs and configuration.")
