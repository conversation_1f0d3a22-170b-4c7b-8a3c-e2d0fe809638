#!/usr/bin/env python3

import json
import requests
import time

def test_system_field_formats():
    """测试 Claude API 的不同 system 字段格式"""
    print("🔧 Testing Claude System Field Format Handling")
    print("━" * 60)
    
    base_url = "http://localhost:8080"
    
    # 测试 1: 简单字符串 system
    print("📝 Test 1: Simple string system")
    simple_system_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 100,
        "system": "You are a helpful assistant.",
        "messages": [
            {
                "role": "user",
                "content": "Hello, what can you help me with?"
            }
        ],
        "stream": False
    }
    
    test_request(base_url, simple_system_request, "Simple String System")
    
    # 测试 2: 复杂内容块数组 system
    print("\n📝 Test 2: Complex content blocks system")
    complex_system_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 100,
        "system": [
            {
                "type": "text",
                "text": "You are a helpful programming assistant. Always provide clear and concise answers."
            }
        ],
        "messages": [
            {
                "role": "user",
                "content": "What is Python?"
            }
        ],
        "stream": False
    }
    
    test_request(base_url, complex_system_request, "Complex Content Blocks System")
    
    # 测试 3: 同时使用复杂 system 和复杂 content
    print("\n📝 Test 3: Complex system + complex content")
    full_complex_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 150,
        "system": [
            {
                "type": "text",
                "text": "You are an expert code reviewer. Analyze code carefully and provide constructive feedback."
            }
        ],
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "Please review this Python function and suggest improvements."
                    }
                ]
            }
        ],
        "stream": False
    }
    
    test_request(base_url, full_complex_request, "Full Complex Format")
    
    # 测试 4: 多个 system 内容块
    print("\n📝 Test 4: Multiple system content blocks")
    multi_system_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 100,
        "system": [
            {
                "type": "text",
                "text": "You are a helpful assistant."
            },
            {
                "type": "text", 
                "text": "Always be polite and professional."
            }
        ],
        "messages": [
            {
                "role": "user",
                "content": "How are you today?"
            }
        ],
        "stream": False
    }
    
    test_request(base_url, multi_system_request, "Multiple System Content Blocks")
    
    # 测试 5: 无 system 字段
    print("\n📝 Test 5: No system field")
    no_system_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 100,
        "messages": [
            {
                "role": "user",
                "content": "Hello!"
            }
        ],
        "stream": False
    }
    
    test_request(base_url, no_system_request, "No System Field")
    
    print("\n━" * 60)
    print("✨ All system field format tests completed!")

def test_request(base_url, request_data, test_name):
    """测试单个请求"""
    print(f"🚀 Testing: {test_name}")
    
    try:
        response = requests.post(
            f"{base_url}/v1/messages",
            json=request_data,
            timeout=30
        )
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Request successful!")
            try:
                response_data = response.json()
                print(f"🆔 Response ID: {response_data.get('id', 'N/A')}")
                print(f"📋 Model: {response_data.get('model', 'N/A')}")
                
                content = response_data.get('content', [])
                if content:
                    for i, block in enumerate(content):
                        if isinstance(block, dict) and block.get('type') == 'text':
                            text = block.get('text', '')
                            print(f"🤖 Response {i+1}: {text[:100]}{'...' if len(text) > 100 else ''}")
                
            except json.JSONDecodeError:
                print("⚠️  Response is not valid JSON")
                print(f"📄 Raw response: {response.text[:200]}")
                
        elif response.status_code == 500:
            print("❌ Server error (500)")
            print(f"📄 Error details: {response.text[:300]}")
            
            # 检查是否还有 JSON 解析错误
            if "JsonDecodingException" in response.text:
                print("🔍 JSON parsing error detected!")
                if "system" in response.text:
                    print("⚠️  System field parsing issue")
                if "content" in response.text:
                    print("⚠️  Content field parsing issue")
        else:
            print(f"⚠️  Unexpected status: {response.status_code}")
            print(f"📄 Response: {response.text[:200]}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def test_edge_cases():
    """测试边界情况"""
    print("\n🔬 Testing Edge Cases")
    print("━" * 40)
    
    base_url = "http://localhost:8080"
    
    # 测试空 system 数组
    print("📝 Test: Empty system array")
    empty_system_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 50,
        "system": [],
        "messages": [
            {
                "role": "user",
                "content": "Hello!"
            }
        ],
        "stream": False
    }
    
    test_request(base_url, empty_system_request, "Empty System Array")
    
    # 测试空字符串 system
    print("\n📝 Test: Empty string system")
    empty_string_system_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 50,
        "system": "",
        "messages": [
            {
                "role": "user",
                "content": "Hello!"
            }
        ],
        "stream": False
    }
    
    test_request(base_url, empty_string_system_request, "Empty String System")

if __name__ == "__main__":
    print("🚀 Starting Claude System Field Tests")
    print("Make sure the server is running on http://localhost:8080")
    print("━" * 60)
    
    # 等待一下确保服务器启动
    time.sleep(2)
    
    # 运行主要测试
    test_system_field_formats()
    
    # 运行边界情况测试
    test_edge_cases()
    
    print("━" * 60)
