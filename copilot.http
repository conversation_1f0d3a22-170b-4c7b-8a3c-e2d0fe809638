### fetch apiToken
GET https://api.github.com/copilot_internal/v2/token
Authorization: token ${key from file}
Accept: application/json

### fetch supported models
GET https://api.githubcopilot.com/models
Authorization: Bearer ${key from copilot_internal request}
Editor-Version: Neovim/0.6.1
Content-Type: application/json
Copilot-Integration-Id: vscode-chat

### Send POST to github copilot
POST https://api.githubcopilot.com/chat/completions
Authorization: Bearer ${key from copilot_internal request}
Editor-Version: Zed/1.89.3
Content-Type: application/json
Copilot-Integration-Id: vscode-chat

{
  "messages": [
    {
      "role": "user",
      "content": "hi!"
    }
  ],
  "intent": false,
  "n": 2,
  "temperature": 0.1,
  "stream": false
}




### fetch apiToken
GET https://api.github.com/copilot_internal/v2/token
Authorization: token ****************************************
Accept: application/json

### fetch supported models
GET https://api.githubcopilot.com/models
Authorization: Bearer tid=06e6abf11a306a4870abad84f7637e63;exp=1748242941;sku=free_engaged_oss;proxy-ep=proxy.individual.githubcopilot.com;st=dotcom;chat=1;cit=1;malfil=1;editor_preview_features=1;ccr=1;rt=1;8kp=1;ip=**************;asn=AS63949:01d25266c2809b57e16736529a6efaf714f62d379c646488d591a750444aa92e
Editor-Version: Neovim/0.6.1
Content-Type: application/json
Copilot-Integration-Id: vscode-chat

### Send POST to github copilot
POST https://api.githubcopilot.com/chat/completions
Authorization: Bearer tid=06e6abf11a306a4870abad84f7637e63;exp=1748242941;sku=free_engaged_oss;proxy-ep=proxy.individual.githubcopilot.com;st=dotcom;chat=1;cit=1;malfil=1;editor_preview_features=1;ccr=1;rt=1;8kp=1;ip=**************;asn=AS63949:01d25266c2809b57e16736529a6efaf714f62d379c646488d591a750444aa92e
Editor-Version: Zed/1.89.3
Content-Type: application/json
Copilot-Integration-Id: vscode-chat

{
  "messages": [
    {
      "role": "user",
      "content": "hi!"
    }
  ],
  "intent": false,
  "n": 2,
  "temperature": 0.1,
  "stream": false
}




### fetch apiToken
GET https://api.github.com/copilot_internal/v2/token
Authorization: token ****************************************
Accept: application/json

### Send POST to github copilot
POST https://api.githubcopilot.com/chat/completions
Authorization: Bearer tid=e48f06bddae42040bec21d3689e6efb0;exp=1748004785;sku=free_engaged_oss;proxy-ep=proxy.individual.githubcopilot.com;st=dotcom;chat=1;cit=1;malfil=1;editor_preview_features=1;ccr=1;8kp=1;ip=**************;asn=AS63949:ef68fca9b140678578000af9aca64a084b747f8a5ab06ea0599139723d8c729b
Editor-Version: Neovim/0.6.1
Content-Type: application/json
Copilot-Integration-Id: vscode-chat
User-Agent:Githubcopilot/1.189.0

{
  "messages": [
    {
      "role": "user",
      "content": "帮我写个kotlin的hello world"
    }
  ],
  "intent": true,
  "model": "claude-3.7-sonnet-thought",
  "stream": true
}