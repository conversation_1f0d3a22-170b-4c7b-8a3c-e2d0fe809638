#!/bin/bash

echo "🔧 Fixing test files to work with new AuthManager parameter..."

# 修复 ClaudeApiTest.kt
sed -i '' 's/configureServer()/configureServer(mockAuthManager)/g' src/test/kotlin/com/github/copilot/llmprovider/api/ClaudeApiTest.kt

# 修复 OpenAIApiTest.kt
sed -i '' 's/configureServer()/configureServer(mockAuthManager)/g' src/test/kotlin/com/github/copilot/llmprovider/api/OpenAIApiTest.kt

# 修复 ServerTest.kt
sed -i '' 's/configureServer()/configureServer(mockAuthManager)/g' src/test/kotlin/com/github/copilot/llmprovider/server/ServerTest.kt

# 添加必要的导入到 OpenAIApiTest.kt
if ! grep -q "import com.github.copilot.llmprovider.auth.AuthManager" src/test/kotlin/com/github/copilot/llmprovider/api/OpenAIApiTest.kt; then
    sed -i '' '3i\
import com.github.copilot.llmprovider.auth.AuthManager
' src/test/kotlin/com/github/copilot/llmprovider/api/OpenAIApiTest.kt
fi

if ! grep -q "import io.mockk.mockk" src/test/kotlin/com/github/copilot/llmprovider/api/OpenAIApiTest.kt; then
    sed -i '' '3i\
import io.mockk.mockk
' src/test/kotlin/com/github/copilot/llmprovider/api/OpenAIApiTest.kt
fi

# 添加必要的导入到 ServerTest.kt
if ! grep -q "import com.github.copilot.llmprovider.auth.AuthManager" src/test/kotlin/com/github/copilot/llmprovider/server/ServerTest.kt; then
    sed -i '' '3i\
import com.github.copilot.llmprovider.auth.AuthManager
' src/test/kotlin/com/github/copilot/llmprovider/server/ServerTest.kt
fi

if ! grep -q "import io.mockk.mockk" src/test/kotlin/com/github/copilot/llmprovider/server/ServerTest.kt; then
    sed -i '' '3i\
import io.mockk.mockk
' src/test/kotlin/com/github/copilot/llmprovider/server/ServerTest.kt
fi

echo "✅ Test files fixed!"
