#!/usr/bin/env python3

import json
import requests
import time

def test_debug_response():
    """测试并查看实际的 GitHub Copilot API 响应"""
    print("🔍 Testing GitHub Copilot API Response Debug")
    print("━" * 60)
    
    base_url = "http://localhost:8080"
    
    # 简单的测试请求
    test_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 50,
        "messages": [
            {
                "role": "user",
                "content": "Hello, please say hi back."
            }
        ],
        "temperature": 0.7
    }
    
    print("🚀 Sending simple test request...")
    print("Expected: Server will print detailed debug information")
    print("Including the actual GitHub Copilot API response structure")
    print("")
    
    try:
        response = requests.post(
            f"{base_url}/v1/messages",
            json=test_request,
            timeout=60
        )
        
        print(f"📊 Client received status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Request successful!")
            try:
                response_data = response.json()
                print("📋 Response structure:")
                print(f"   ID: {response_data.get('id', 'N/A')}")
                print(f"   Model: {response_data.get('model', 'N/A')}")
                print(f"   Content blocks: {len(response_data.get('content', []))}")
                
                content = response_data.get('content', [])
                if content:
                    for i, block in enumerate(content):
                        if isinstance(block, dict) and block.get('type') == 'text':
                            text = block.get('text', '')
                            print(f"   Block {i+1}: {text[:100]}{'...' if len(text) > 100 else ''}")
                            
            except json.JSONDecodeError:
                print("⚠️  Response is not valid JSON")
                print(f"📄 Raw response: {response.text[:300]}")
                
        elif response.status_code == 500:
            print("❌ Server error (500)")
            print("📄 Check server console for detailed error information")
            print("Expected to see:")
            print("  - Converted OpenAI Request details")
            print("  - Raw GitHub Copilot API response")
            print("  - JSON parsing error details")
            print("")
            print(f"Error response: {response.text[:500]}")
            
        else:
            print(f"⚠️  Unexpected status: {response.status_code}")
            print(f"📄 Response: {response.text[:300]}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - make sure server is running")
        print("Start server with: ./run_debug_test.sh")
    except requests.exceptions.Timeout:
        print("⏰ Request timeout")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    print("🚀 Starting GitHub Copilot API Response Debug Test")
    print("Make sure the server is running on http://localhost:8080")
    print("━" * 60)
    print("This test will trigger detailed debug output on the server console.")
    print("Watch the server logs to see:")
    print("  1. 🔍 Flexible Claude Request parsing")
    print("  2. 🔄 Converted OpenAI Request details")
    print("  3. 📤 JSON sent to GitHub Copilot API")
    print("  4. 📥 Raw response from GitHub Copilot API")
    print("  5. ❌ Any JSON parsing errors with details")
    print("━" * 60)
    
    # 运行测试
    test_debug_response()
    
    print("━" * 60)
    print("✨ Debug test completed!")
    print("Check the server console output for detailed information.")
    print("Look for the GitHub Copilot API response structure to understand")
    print("what fields are actually returned vs what our model expects.")
