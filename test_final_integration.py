#!/usr/bin/env python3

import json
import requests
import time

def test_final_integration():
    """最终集成测试 - 验证所有 JSON 解析问题已解决"""
    print("🎯 Final Integration Test - GitHub Copilot Claude LLM Provider")
    print("━" * 70)
    
    base_url = "http://localhost:8080"
    
    tests = [
        # 测试 1: 基础功能
        {
            "name": "Basic Functionality",
            "request": {
                "model": "claude-3.5-sonnet",
                "max_tokens": 100,
                "messages": [
                    {
                        "role": "user",
                        "content": "Hello! Please tell me a short joke."
                    }
                ],
                "temperature": 0.8
            }
        },
        
        # 测试 2: 代码生成
        {
            "name": "Code Generation",
            "request": {
                "model": "claude-3.5-sonnet",
                "max_tokens": 200,
                "system": "You are an expert Python programmer.",
                "messages": [
                    {
                        "role": "user",
                        "content": "Write a Python function to calculate fibonacci numbers."
                    }
                ],
                "temperature": 0.3
            }
        },
        
        # 测试 3: 复杂对话
        {
            "name": "Complex Conversation",
            "request": {
                "model": "claude-3.5-sonnet",
                "max_tokens": 250,
                "messages": [
                    {
                        "role": "user",
                        "content": "What are the main differences between Python and JavaScript?"
                    },
                    {
                        "role": "assistant",
                        "content": "Python and JavaScript are both popular programming languages, but they have several key differences in syntax, use cases, and execution environments."
                    },
                    {
                        "role": "user",
                        "content": "Can you give me specific examples of their syntax differences?"
                    }
                ],
                "temperature": 0.5
            }
        },
        
        # 测试 4: 复杂格式（数组内容）
        {
            "name": "Complex Array Format",
            "request": {
                "model": "claude-3.5-sonnet",
                "max_tokens": 150,
                "system": [
                    {
                        "type": "text",
                        "text": "You are a helpful technical writer."
                    }
                ],
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "Explain what REST API means in simple terms."
                            }
                        ]
                    }
                ],
                "temperature": 0.4
            }
        },
        
        # 测试 5: 带元数据的请求
        {
            "name": "Request with Metadata",
            "request": {
                "model": "claude-3-5-haiku-20241022",
                "max_tokens": 100,
                "messages": [
                    {
                        "role": "user",
                        "content": "What is machine learning?"
                    }
                ],
                "metadata": {
                    "user_id": "test_user_final",
                    "session_id": "final_test_session",
                    "feature": "final_integration_test"
                },
                "temperature": 0.6
            }
        }
    ]
    
    success_count = 0
    total_count = len(tests)
    
    print(f"Running {total_count} comprehensive tests...")
    print("━" * 70)
    
    for i, test in enumerate(tests, 1):
        print(f"\n📝 Test {i}/{total_count}: {test['name']}")
        print("─" * 50)
        
        success = test_request(base_url, test['request'], test['name'])
        if success:
            success_count += 1
        
        time.sleep(1.5)  # 给服务器一些处理时间
    
    print(f"\n━" * 70)
    print(f"📊 Final Test Results: {success_count}/{total_count} tests passed")
    
    if success_count == total_count:
        print("🎉 ALL TESTS PASSED! 🎉")
        print("✅ GitHub Copilot Claude LLM Provider is fully functional!")
        print("✅ All JSON parsing issues have been resolved!")
        print("✅ Real AI responses are being returned correctly!")
        print("✅ All Claude API formats are supported!")
        return True
    else:
        print(f"⚠️  {total_count - success_count} tests failed.")
        print("Some issues may still need to be addressed.")
        return False

def test_request(base_url, request_data, test_name):
    """测试单个请求，返回是否成功"""
    try:
        response = requests.post(
            f"{base_url}/v1/messages",
            json=request_data,
            timeout=60
        )
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Success!")
            try:
                response_data = response.json()
                
                # 验证响应结构
                required_fields = ['id', 'type', 'role', 'content', 'model']
                missing_fields = [field for field in required_fields if field not in response_data]
                
                if missing_fields:
                    print(f"⚠️  Missing required fields: {missing_fields}")
                    return False
                
                # 验证内容
                content = response_data.get('content', [])
                if content and isinstance(content, list) and len(content) > 0:
                    first_block = content[0]
                    if isinstance(first_block, dict) and first_block.get('type') == 'text':
                        text = first_block.get('text', '')
                        if text and len(text.strip()) > 5:
                            print(f"🤖 Response: {text[:60]}{'...' if len(text) > 60 else ''}")
                            print("✅ Valid AI response received!")
                            
                            # 验证响应质量
                            if "mock" not in text.lower() and "placeholder" not in text.lower():
                                print("✅ Real AI content confirmed!")
                                return True
                            else:
                                print("⚠️  Still receiving mock/placeholder content")
                                return False
                        else:
                            print("⚠️  Response content too short or empty")
                            return False
                    else:
                        print("⚠️  Invalid content block structure")
                        return False
                else:
                    print("⚠️  No valid content in response")
                    return False
                    
            except json.JSONDecodeError as e:
                print("❌ Response is not valid JSON")
                print(f"   JSON Error: {e}")
                return False
                
        elif response.status_code == 500:
            print("❌ Server error (500)")
            error_text = response.text
            
            # 检查具体的错误类型
            if any(keyword in error_text for keyword in [
                "JsonDecodingException",
                "JsonConvertException",
                "required for type",
                "unknown key",
                "missing at path"
            ]):
                print("🔍 JSON parsing error detected!")
                print("   This indicates model definitions still need adjustment.")
                print(f"   Error details: {error_text[:200]}")
                return False
            elif "authentication" in error_text.lower():
                print("🔑 Authentication error - check GitHub Copilot setup")
                return False
            else:
                print("⚠️  Other server error")
                print(f"   Error: {error_text[:150]}")
                return False
                
        elif response.status_code in [401, 403]:
            print(f"🔑 Authentication error ({response.status_code})")
            print("💡 Check GitHub Copilot subscription and token")
            return False
            
        else:
            print(f"⚠️  Unexpected status: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ Request timeout")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - make sure server is running")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 GitHub Copilot Claude LLM Provider - Final Integration Test")
    print("Make sure the server is running on http://localhost:8080")
    print("━" * 70)
    print("This comprehensive test verifies:")
    print("  ✅ All JSON parsing issues are resolved")
    print("  ✅ Real GitHub Copilot AI responses are returned")
    print("  ✅ All Claude API formats are supported")
    print("  ✅ Error handling works correctly")
    print("  ✅ Authentication and authorization work")
    print("━" * 70)
    
    # 检查服务器状态
    try:
        response = requests.get("http://localhost:8080/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running and healthy")
        else:
            print("⚠️  Server responded but may not be healthy")
    except:
        print("❌ Server is not running. Please start it first:")
        print("   ./run_debug_test.sh")
        exit(1)
    
    print("━" * 70)
    
    # 运行最终测试
    success = test_final_integration()
    
    print("━" * 70)
    if success:
        print("🎉🎉🎉 FINAL INTEGRATION TEST PASSED! 🎉🎉🎉")
        print("GitHub Copilot Claude LLM Provider is ready for production use!")
    else:
        print("❌ Final integration test failed.")
        print("Please check the errors above and address any remaining issues.")
