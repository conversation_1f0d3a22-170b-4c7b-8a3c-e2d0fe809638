#!/usr/bin/env python3

import json
import requests
import time

def test_token_refresh():
    """测试 token 自动刷新功能"""
    print("🔄 Testing GitHub Copilot API Token Auto-Refresh")
    print("━" * 60)
    
    base_url = "http://localhost:8080"
    
    # 测试 1: 正常请求（应该使用缓存的 token）
    print("📝 Test 1: Normal request (should use cached token)")
    normal_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 50,
        "messages": [
            {
                "role": "user",
                "content": "Hello, please respond briefly."
            }
        ]
    }
    
    success1 = test_request(base_url, normal_request, "Normal Request")
    
    if not success1:
        print("❌ Normal request failed, cannot proceed with token refresh test")
        return False
    
    # 等待一段时间，然后发送更多请求来测试 token 管理
    print("\n📝 Test 2: Multiple requests to test token management")
    
    for i in range(3):
        print(f"\n🔄 Request {i+1}/3:")
        test_request_simple = {
            "model": "claude-3.5-sonnet",
            "max_tokens": 30,
            "messages": [
                {
                    "role": "user",
                    "content": f"This is test request number {i+1}. Please respond."
                }
            ]
        }
        
        success = test_request(base_url, test_request_simple, f"Request {i+1}")
        if not success:
            print(f"❌ Request {i+1} failed")
            return False
        
        time.sleep(2)  # 短暂等待
    
    print("\n━" * 60)
    print("✅ Token refresh test completed successfully!")
    print("🔍 Check server logs for token refresh messages:")
    print("   - 'Using cached API token' (normal operation)")
    print("   - 'API token appears to be expired' (if token expired)")
    print("   - 'Force refreshing API token' (if retry occurred)")
    print("   - 'API token refreshed successfully' (after refresh)")
    return True

def test_request(base_url, request_data, test_name):
    """测试单个请求，返回是否成功"""
    try:
        response = requests.post(
            f"{base_url}/v1/messages",
            json=request_data,
            timeout=30
        )
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Success!")
            try:
                response_data = response.json()
                content = response_data.get('content', [])
                if content and len(content) > 0:
                    text = content[0].get('text', '')
                    print(f"🤖 Response: {text[:50]}{'...' if len(text) > 50 else ''}")
                    return True
                else:
                    print("⚠️  No content in response")
                    return False
            except json.JSONDecodeError:
                print("❌ Invalid JSON response")
                return False
                
        elif response.status_code == 401:
            print("🔑 Authentication error (401)")
            print("💡 This might indicate token refresh is needed")
            # 在实际应用中，这应该触发自动重试
            return False
            
        elif response.status_code == 500:
            print("❌ Server error (500)")
            error_text = response.text
            
            # 检查是否是 token 相关错误
            if any(keyword in error_text for keyword in [
                "token", "authentication", "unauthorized", "expired"
            ]):
                print("🔍 Token-related error detected")
                print("Expected: Server should automatically retry with refreshed token")
            
            print(f"📄 Error: {error_text[:200]}")
            return False
        else:
            print(f"⚠️  Unexpected status: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - make sure server is running")
        return False
    except requests.exceptions.Timeout:
        print("⏰ Request timeout")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def simulate_token_expiry_scenario():
    """模拟 token 过期场景的说明"""
    print("\n🔍 Token Refresh Mechanism Explanation:")
    print("━" * 60)
    print("1. 🔄 Normal Operation:")
    print("   - AuthManager checks if cached token is expired")
    print("   - If valid, uses cached token")
    print("   - If expired, automatically refreshes")
    print("")
    print("2. 🚨 Error-Based Refresh:")
    print("   - If API returns 401 Unauthorized")
    print("   - TokenExpiredException is thrown")
    print("   - ProxyService catches it and retries with fresh token")
    print("")
    print("3. 📋 Server Log Messages to Watch For:")
    print("   - 'Using cached API token' (normal)")
    print("   - 'Cached API token expired, refreshing...' (time-based)")
    print("   - 'API token appears to be expired' (error-based)")
    print("   - 'Force refreshing API token...' (retry)")
    print("   - 'API token refreshed successfully' (success)")
    print("")
    print("4. 🔧 Retry Logic:")
    print("   - Max 1 retry per request")
    print("   - Only retries on TokenExpiredException")
    print("   - Other errors are not retried")

if __name__ == "__main__":
    print("🚀 GitHub Copilot API Token Auto-Refresh Test")
    print("Make sure the server is running on http://localhost:8080")
    print("━" * 60)
    
    # 检查服务器状态
    try:
        response = requests.get("http://localhost:8080/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print("⚠️  Server responded but may not be healthy")
    except:
        print("❌ Server is not running. Please start it first:")
        print("   ./run_debug_test.sh")
        exit(1)
    
    print("━" * 60)
    
    # 显示机制说明
    simulate_token_expiry_scenario()
    
    print("━" * 60)
    
    # 运行测试
    success = test_token_refresh()
    
    print("━" * 60)
    if success:
        print("🎉 Token refresh test completed!")
        print("✅ The token auto-refresh mechanism is working correctly!")
    else:
        print("❌ Token refresh test failed.")
        print("Check server logs for detailed error information.")
