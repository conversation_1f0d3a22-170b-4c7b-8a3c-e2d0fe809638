#!/usr/bin/env python3

import json
import requests
import time

def test_dynamic_json_handling():
    """测试动态 JSON 结构处理"""
    print("🔄 Testing Dynamic JSON Structure Handling")
    print("━" * 60)
    
    base_url = "http://localhost:8080"
    
    # 测试 1: 简单字符串 content（来自实际请求）
    print("📝 Test 1: Simple string content (real request format)")
    simple_request = {
        "model": "claude-3-5-haiku-20241022",
        "max_tokens": 1,
        "messages": [
            {
                "role": "user",
                "content": "test"
            }
        ],
        "temperature": 0,
        "metadata": {
            "user_id": "8829af087e827e3a455e1326c6d042f6959c1512ee00d1914648e6c1ed201d61"
        }
    }
    
    test_request(base_url, simple_request, "Simple String Content")
    
    # 测试 2: 复杂数组 content（来自实际请求）
    print("\n📝 Test 2: Complex array content (real request format)")
    complex_request = {
        "model": "claude-sonnet-4-20250514",
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "Hello, how are you?"
                    },
                    {
                        "type": "text",
                        "text": "hi"
                    },
                    {
                        "type": "text",
                        "text": "Please help me with this task.",
                        "cache_control": {
                            "type": "ephemeral"
                        }
                    }
                ]
            }
        ],
        "temperature": 1,
        "system": [
            {
                "type": "text",
                "text": "You are a helpful assistant.",
                "cache_control": {
                    "type": "ephemeral"
                }
            },
            {
                "type": "text",
                "text": "Always be polite and professional.",
                "cache_control": {
                    "type": "ephemeral"
                }
            }
        ],
        "tools": [
            {
                "name": "Task",
                "description": "Execute a specific task",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "description": {
                            "type": "string",
                            "description": "A short (3-5 word) description of the task"
                        },
                        "prompt": {
                            "type": "string",
                            "description": "The task for the agent to perform"
                        }
                    },
                    "required": [
                        "description",
                        "prompt"
                    ],
                    "additionalProperties": False,
                    "$schema": "http://json-schema.org/draft-07/schema#"
                }
            }
        ],
        "metadata": {
            "user_id": "8829af087e827e3a455e1326c6d042f6959c1512ee00d1914648e6c1ed201d61"
        },
        "max_tokens": 21333
    }
    
    test_request(base_url, complex_request, "Complex Array Content")
    
    # 测试 3: 混合格式
    print("\n📝 Test 3: Mixed format")
    mixed_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 100,
        "system": "You are helpful.",  # 简单字符串
        "messages": [
            {
                "role": "user",
                "content": [  # 复杂数组
                    {
                        "type": "text",
                        "text": "What is the weather?"
                    }
                ]
            },
            {
                "role": "assistant",
                "content": "I don't have access to weather data."  # 简单字符串
            },
            {
                "role": "user", 
                "content": "Thanks anyway!"  # 简单字符串
            }
        ],
        "stream": False
    }
    
    test_request(base_url, mixed_request, "Mixed Format")
    
    # 测试 4: 空内容和边界情况
    print("\n📝 Test 4: Edge cases")
    edge_cases_request = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 50,
        "messages": [
            {
                "role": "user",
                "content": ""  # 空字符串
            }
        ],
        "stream": False
    }
    
    test_request(base_url, edge_cases_request, "Edge Cases")
    
    print("\n━" * 60)
    print("✨ Dynamic JSON structure tests completed!")

def test_request(base_url, request_data, test_name):
    """测试单个请求"""
    print(f"🚀 Testing: {test_name}")
    
    try:
        response = requests.post(
            f"{base_url}/v1/messages",
            json=request_data,
            timeout=30
        )
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Request successful!")
            try:
                response_data = response.json()
                print(f"🆔 Response ID: {response_data.get('id', 'N/A')}")
                print(f"📋 Model: {response_data.get('model', 'N/A')}")
                
                content = response_data.get('content', [])
                if content:
                    for i, block in enumerate(content):
                        if isinstance(block, dict) and block.get('type') == 'text':
                            text = block.get('text', '')
                            print(f"🤖 Response {i+1}: {text[:100]}{'...' if len(text) > 100 else ''}")
                
            except json.JSONDecodeError:
                print("⚠️  Response is not valid JSON")
                print(f"📄 Raw response: {response.text[:200]}")
                
        elif response.status_code == 400:
            print("⚠️  Bad Request (400)")
            print(f"📄 Error details: {response.text[:300]}")
        elif response.status_code == 500:
            print("❌ Server error (500)")
            print(f"📄 Error details: {response.text[:300]}")
            
            # 检查是否还有序列化错误
            if "SerializationException" in response.text or "JsonDecodingException" in response.text:
                print("🔍 Serialization error detected!")
                if "type" in response.text and "discriminator" in response.text:
                    print("⚠️  Type discriminator conflict issue")
                if "sealed class" in response.text.lower():
                    print("⚠️  Sealed class serialization issue")
            else:
                print("✅ No serialization errors detected")
        else:
            print(f"⚠️  Unexpected status: {response.status_code}")
            print(f"📄 Response: {response.text[:200]}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    print("🚀 Starting Dynamic JSON Structure Tests")
    print("Make sure the server is running on http://localhost:8080")
    print("━" * 60)
    print("This test uses real request formats that caused previous errors.")
    print("Watch the server debug output for detailed information.")
    print("━" * 60)
    
    # 等待一下确保服务器启动
    time.sleep(2)
    
    # 运行测试
    test_dynamic_json_handling()
    
    print("━" * 60)
