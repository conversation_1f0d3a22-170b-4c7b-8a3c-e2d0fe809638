#!/usr/bin/env python3

import json
import requests
import time
import sys

def test_real_copilot_integration():
    """测试真实的 GitHub Copilot Claude LLM Provider 集成"""
    print("🚀 Testing Real GitHub Copilot Claude LLM Provider Integration")
    print("━" * 70)
    
    base_url = "http://localhost:8080"
    
    tests = [
        # 测试 1: 简单的代码生成请求
        {
            "name": "Simple Code Generation",
            "request": {
                "model": "claude-3.5-sonnet",
                "max_tokens": 200,
                "messages": [
                    {
                        "role": "user",
                        "content": "Write a simple Python function to calculate the factorial of a number."
                    }
                ],
                "temperature": 0.7
            }
        },
        
        # 测试 2: 代码解释请求
        {
            "name": "Code Explanation",
            "request": {
                "model": "claude-3.5-sonnet",
                "max_tokens": 300,
                "system": "You are a helpful programming assistant.",
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "Explain this Python code:\n\ndef quicksort(arr):\n    if len(arr) <= 1:\n        return arr\n    pivot = arr[len(arr) // 2]\n    left = [x for x in arr if x < pivot]\n    middle = [x for x in arr if x == pivot]\n    right = [x for x in arr if x > pivot]\n    return quicksort(left) + middle + quicksort(right)"
                            }
                        ]
                    }
                ],
                "temperature": 0.3
            }
        },
        
        # 测试 3: 复杂的系统提示和多轮对话
        {
            "name": "Complex System Prompt with Multi-turn",
            "request": {
                "model": "claude-3.5-sonnet",
                "max_tokens": 400,
                "system": [
                    {
                        "type": "text",
                        "text": "You are an expert software architect with deep knowledge of design patterns and best practices.",
                        "cache_control": {
                            "type": "ephemeral"
                        }
                    }
                ],
                "messages": [
                    {
                        "role": "user",
                        "content": "I'm building a web application. What design patterns should I consider?"
                    },
                    {
                        "role": "assistant",
                        "content": "For web applications, I'd recommend considering these key design patterns: MVC (Model-View-Controller) for separation of concerns, Repository pattern for data access, and Dependency Injection for loose coupling."
                    },
                    {
                        "role": "user",
                        "content": "Can you give me a specific example of implementing the Repository pattern in Python?"
                    }
                ],
                "temperature": 0.5
            }
        },
        
        # 测试 4: 带元数据的请求（模拟真实使用场景）
        {
            "name": "Request with Metadata (Real Use Case)",
            "request": {
                "model": "claude-3-5-haiku-20241022",
                "max_tokens": 150,
                "messages": [
                    {
                        "role": "user",
                        "content": "What's the difference between async and await in JavaScript?"
                    }
                ],
                "temperature": 0,
                "metadata": {
                    "user_id": "test_user_12345",
                    "session_id": "session_67890",
                    "request_source": "vscode_extension",
                    "feature": "code_explanation"
                }
            }
        },
        
        # 测试 5: 流式响应测试
        {
            "name": "Streaming Response",
            "request": {
                "model": "claude-3.5-sonnet",
                "max_tokens": 200,
                "messages": [
                    {
                        "role": "user",
                        "content": "Write a short story about a programmer who discovers a bug that changes reality."
                    }
                ],
                "stream": True,
                "temperature": 0.8
            }
        }
    ]
    
    success_count = 0
    total_count = len(tests)
    
    for i, test in enumerate(tests, 1):
        print(f"\n📝 Test {i}/{total_count}: {test['name']}")
        print("─" * 50)
        
        success = test_request(base_url, test['request'], test['name'])
        if success:
            success_count += 1
        
        time.sleep(2)  # 给服务器一些时间处理
    
    print(f"\n━" * 70)
    print(f"📊 Test Results: {success_count}/{total_count} tests passed")
    
    if success_count == total_count:
        print("🎉 All tests passed! Real GitHub Copilot integration is working!")
        return True
    else:
        print(f"⚠️  {total_count - success_count} tests failed. Check server logs for details.")
        return False

def test_request(base_url, request_data, test_name):
    """测试单个请求，返回是否成功"""
    try:
        is_streaming = request_data.get('stream', False)
        
        if is_streaming:
            return test_streaming_request(base_url, request_data, test_name)
        else:
            return test_json_request(base_url, request_data, test_name)
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def test_json_request(base_url, request_data, test_name):
    """测试 JSON 响应请求"""
    try:
        response = requests.post(
            f"{base_url}/v1/messages",
            json=request_data,
            timeout=60  # 增加超时时间，因为真实 API 调用可能较慢
        )
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Success!")
            try:
                response_data = response.json()
                
                # 检查响应结构
                if 'content' in response_data and response_data['content']:
                    content = response_data['content']
                    if isinstance(content, list) and len(content) > 0:
                        first_block = content[0]
                        if isinstance(first_block, dict) and first_block.get('type') == 'text':
                            text = first_block.get('text', '')
                            print(f"🤖 Response Preview: {text[:100]}{'...' if len(text) > 100 else ''}")
                            
                            # 检查是否是真实响应（不是模拟响应）
                            if "mock" in text.lower() or "this will be replaced" in text.lower():
                                print("⚠️  Warning: Still receiving mock responses!")
                                return False
                            else:
                                print("✅ Received real AI response!")
                                return True
                        else:
                            print("⚠️  Unexpected content block structure")
                            return False
                    else:
                        print("⚠️  Empty or invalid content array")
                        return False
                else:
                    print("⚠️  No content in response")
                    return False
                    
            except json.JSONDecodeError:
                print("❌ Response is not valid JSON")
                print(f"📄 Raw response: {response.text[:200]}")
                return False
                
        elif response.status_code == 401:
            print("❌ Authentication failed - check GitHub Copilot token")
            print("💡 Make sure you have a valid GitHub Copilot subscription and token")
            return False
            
        elif response.status_code == 403:
            print("❌ Forbidden - check GitHub Copilot permissions")
            return False
            
        elif response.status_code == 500:
            print("❌ Server error (500)")
            error_text = response.text[:500]
            print(f"📄 Error: {error_text}")
            
            # 检查常见错误
            if "authentication" in error_text.lower():
                print("💡 Hint: Authentication issue - check your GitHub Copilot setup")
            elif "quota" in error_text.lower() or "rate limit" in error_text.lower():
                print("💡 Hint: Rate limit or quota exceeded")
            
            return False
        else:
            print(f"⚠️  Unexpected status: {response.status_code}")
            print(f"📄 Response: {response.text[:200]}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ Request timeout - API call took too long")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - make sure server is running")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_streaming_request(base_url, request_data, test_name):
    """测试流式响应请求"""
    try:
        response = requests.post(
            f"{base_url}/v1/messages",
            json=request_data,
            stream=True,
            timeout=60
        )
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Streaming started!")
            
            chunk_count = 0
            total_content = ""
            
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data: '):
                        data_str = line_str[6:]  # Remove 'data: ' prefix
                        
                        if data_str == '[DONE]':
                            print("🏁 Streaming completed!")
                            break
                        
                        try:
                            chunk_data = json.loads(data_str)
                            if 'content' in chunk_data:
                                content = chunk_data['content']
                                if isinstance(content, list) and len(content) > 0:
                                    text = content[0].get('text', '')
                                    total_content += text
                                    chunk_count += 1
                        except json.JSONDecodeError:
                            pass  # Skip invalid JSON chunks
            
            print(f"📊 Received {chunk_count} chunks")
            if total_content:
                print(f"🤖 Content Preview: {total_content[:100]}{'...' if len(total_content) > 100 else ''}")
                
                # 检查是否是真实响应
                if "mock" in total_content.lower() or "this will be replaced" in total_content.lower():
                    print("⚠️  Warning: Still receiving mock responses!")
                    return False
                else:
                    print("✅ Received real streaming AI response!")
                    return True
            else:
                print("⚠️  No content received in stream")
                return False
        else:
            print(f"❌ Streaming failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Streaming error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Real GitHub Copilot Claude LLM Provider Integration Tests")
    print("Make sure the server is running on http://localhost:8080")
    print("━" * 70)
    
    # 检查服务器是否运行
    try:
        response = requests.get("http://localhost:8080/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print("⚠️  Server responded but may not be healthy")
    except:
        print("❌ Server is not running. Please start it first:")
        print("   ./run_debug_test.sh")
        sys.exit(1)
    
    print("━" * 70)
    print("⚠️  Important: These tests require a valid GitHub Copilot subscription!")
    print("Make sure you have:")
    print("- Valid GitHub Copilot subscription")
    print("- Proper authentication setup")
    print("- Network access to GitHub Copilot API")
    print("━" * 70)
    
    # 运行测试
    success = test_real_copilot_integration()
    
    print("━" * 70)
    if success:
        print("🎉 All real integration tests completed successfully!")
        print("GitHub Copilot Claude LLM Provider is working correctly!")
        sys.exit(0)
    else:
        print("❌ Some tests failed. Check the output above.")
        print("Common issues:")
        print("- Authentication: Check GitHub Copilot token")
        print("- Subscription: Ensure active GitHub Copilot subscription")
        print("- Network: Check internet connection")
        sys.exit(1)
