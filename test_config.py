#!/usr/bin/env python3

import json
import os
from pathlib import Path

def test_github_copilot_config():
    """测试 GitHub Copilot 配置读取"""
    print("🔍 Testing GitHub Copilot Configuration")
    print("━" * 50)
    
    home_dir = Path.home()
    config_path = home_dir / ".config" / "github-copilot" / "apps.json"
    
    print(f"📁 Home directory: {home_dir}")
    print(f"📄 Config path: {config_path}")
    print(f"📋 Config file exists: {config_path.exists()}")
    
    if config_path.exists():
        print("✅ GitHub Copilot config file found!")
        
        try:
            with open(config_path, 'r') as f:
                config_data = json.load(f)
            
            print(f"📏 Config keys: {list(config_data.keys())}")
            
            # 查找目标客户端 ID
            target_client_id = "Iv23ctfURkiMfJ4xr5mv"
            target_key = f"github.com:{target_client_id}"
            
            if target_key in config_data:
                print(f"🎯 Target client ID ({target_client_id}) found!")
                target_config = config_data[target_key]
                
                oauth_token = target_config.get("oauth_token", "")
                user = target_config.get("user", "")
                
                print(f"👤 User: {user}")
                print(f"🔑 OAuth token: {oauth_token[:10]}...{oauth_token[-10:]} ({len(oauth_token)} chars)")
                
                return oauth_token
            else:
                print(f"⚠️  Target client ID ({target_client_id}) not found")
                print("Available keys:")
                for key in config_data.keys():
                    print(f"   - {key}")
                
                # 返回第一个可用的 token
                for key, value in config_data.items():
                    if "oauth_token" in value:
                        token = value["oauth_token"]
                        print(f"🔄 Using token from {key}: {token[:10]}...{token[-10:]}")
                        return token
                        
        except Exception as e:
            print(f"❌ Error reading config: {e}")
    else:
        print("❌ GitHub Copilot config file not found")
    
    return None

def test_github_copilot_api(oauth_token):
    """测试 GitHub Copilot API 调用"""
    if not oauth_token:
        print("❌ No OAuth token available for API testing")
        return
        
    print("\n🚀 Testing GitHub Copilot API")
    print("━" * 50)
    
    import requests
    
    try:
        # 获取 API token
        print("📡 Getting API token...")
        response = requests.get(
            "https://api.github.com/copilot_internal/v2/token",
            headers={
                "Authorization": f"token {oauth_token}",
                "Accept": "application/json",
                "User-Agent": "GitHub-Copilot-LLM-Provider/1.0"
            }
        )
        
        if response.status_code == 200:
            api_token_data = response.json()
            api_token = api_token_data["token"]
            expires_at = api_token_data["expires_at"]
            
            print(f"✅ API token obtained!")
            print(f"🔑 Token: {api_token[:20]}...{api_token[-10:]}")
            print(f"⏰ Expires at: {expires_at}")
            
            # 获取支持的模型
            print("\n📋 Getting supported models...")
            models_response = requests.get(
                "https://api.githubcopilot.com/models",
                headers={
                    "Authorization": f"Bearer {api_token}",
                    "Accept": "application/json",
                    "User-Agent": "GitHub-Copilot-LLM-Provider/1.0",
                    "Editor-Version": "vscode/1.95.0",
                    "Editor-Plugin-Version": "copilot/1.0.0"
                }
            )
            
            if models_response.status_code == 200:
                models_data = models_response.json()
                models = models_data.get("data", [])
                
                print(f"✅ Found {len(models)} supported models:")
                for model in models:
                    print(f"   📋 {model['id']} ({model.get('owned_by', 'unknown')})")
                
                # 查找 Claude 模型
                claude_models = [m for m in models if 'claude' in m['id'].lower()]
                if claude_models:
                    preferred_model = claude_models[0]['id']
                    print(f"\n🎯 Preferred Claude model: {preferred_model}")
                    return preferred_model
                else:
                    print("\n⚠️  No Claude models found")
                    if models:
                        fallback_model = models[0]['id']
                        print(f"🔄 Using fallback model: {fallback_model}")
                        return fallback_model
            else:
                print(f"❌ Failed to get models: {models_response.status_code}")
                print(f"Response: {models_response.text}")
        else:
            print(f"❌ Failed to get API token: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ API test failed: {e}")
    
    return None

if __name__ == "__main__":
    # 测试配置读取
    oauth_token = test_github_copilot_config()
    
    # 测试 API 调用
    if oauth_token:
        preferred_model = test_github_copilot_api(oauth_token)
        
        if preferred_model:
            print(f"\n🎉 GitHub Copilot integration test successful!")
            print(f"🔑 OAuth token: Available")
            print(f"🤖 Preferred model: {preferred_model}")
        else:
            print(f"\n⚠️  Partial success - OAuth token available but API test failed")
    else:
        print(f"\n❌ GitHub Copilot integration test failed - no OAuth token")
    
    print("\n━" * 50)
    print("✨ Test completed!")
