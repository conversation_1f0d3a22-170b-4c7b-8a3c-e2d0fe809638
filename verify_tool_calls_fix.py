#!/usr/bin/env python3

import json
import requests
import time

def verify_tool_calls_fix():
    """验证 tool_calls 转换修复"""
    print("🔍 Verifying Tool Calls Conversion Fix")
    print("━" * 50)
    
    base_url = "http://localhost:8080"
    
    # 检查服务器状态
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print("⚠️  Server responded but may not be healthy")
            return False
    except:
        print("❌ Server is not running. Please start it first:")
        print("   ./run_debug_test.sh")
        return False
    
    print("━" * 50)
    
    # 简单的工具调用测试
    print("📝 Testing simple tool call conversion...")
    
    request_data = {
        "model": "claude-3.5-sonnet",
        "max_tokens": 100,
        "messages": [
            {
                "role": "user",
                "content": "List files in the current directory."
            }
        ],
        "tools": [
            {
                "name": "LS",
                "description": "List files and directories",
                "input_schema": {
                    "type": "object",
                    "properties": {
                        "path": {
                            "type": "string",
                            "description": "Directory path to list"
                        }
                    },
                    "required": ["path"]
                }
            }
        ]
    }
    
    try:
        print("🚀 Sending request...")
        response = requests.post(
            f"{base_url}/v1/messages",
            json=request_data,
            timeout=60
        )
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Request successful!")
            
            try:
                response_data = response.json()
                
                # 分析响应格式
                print("\n🔍 Response Analysis:")
                print(f"  📋 Response type: {response_data.get('type', 'unknown')}")
                print(f"  🆔 ID: {response_data.get('id', 'no-id')}")
                print(f"  🤖 Model: {response_data.get('model', 'unknown')}")
                print(f"  🏁 Stop reason: {response_data.get('stop_reason', 'unknown')}")
                
                # 检查内容块
                content = response_data.get('content', [])
                print(f"  📋 Content blocks: {len(content)}")
                
                has_tool_use = False
                has_text = False
                
                for i, block in enumerate(content):
                    block_type = block.get('type', 'unknown')
                    print(f"    {i+1}. Type: {block_type}")
                    
                    if block_type == 'text':
                        has_text = True
                        text = block.get('text', '')
                        print(f"       Text: {text[:50]}{'...' if len(text) > 50 else ''}")
                        
                    elif block_type == 'tool_use':
                        has_tool_use = True
                        tool_id = block.get('id', 'no-id')
                        tool_name = block.get('name', 'no-name')
                        tool_input = block.get('input', {})
                        print(f"       ✅ Tool: {tool_name}")
                        print(f"       ✅ ID: {tool_id}")
                        print(f"       ✅ Input: {json.dumps(tool_input, indent=10)}")
                
                # 验证结果
                print("\n📊 Verification Results:")
                
                if has_tool_use:
                    print("  ✅ PASS: Response contains tool_use blocks!")
                    print("  ✅ PASS: OpenAI tool_calls → Claude tool_use conversion working!")
                else:
                    print("  ❌ FAIL: No tool_use blocks found")
                    return False
                
                if response_data.get('stop_reason') == 'tool_use':
                    print("  ✅ PASS: Correct stop_reason for tool calls!")
                else:
                    print(f"  ⚠️  WARN: Stop reason is '{response_data.get('stop_reason')}', expected 'tool_use'")
                
                print("\n🎉 Tool calls conversion is working correctly!")
                return True
                
            except json.JSONDecodeError:
                print("❌ Invalid JSON response")
                return False
                
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"📄 Error: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ Request error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Tool Calls Conversion Fix Verification")
    print("This test verifies that OpenAI tool_calls are correctly")
    print("converted to Claude tool_use format in responses.")
    print("━" * 50)
    
    success = verify_tool_calls_fix()
    
    print("━" * 50)
    if success:
        print("🎉 VERIFICATION PASSED!")
        print("Tool calls conversion fix is working correctly!")
    else:
        print("❌ Verification failed.")
        print("Check server logs for conversion issues.")
